{"name": "nodejs-backend-template", "version": "1.0.0", "description": "A clean Node.js backend template with MongoDB and Redis", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "license": "MIT", "dependencies": {"async": "^3.2.4", "bcryptjs": "^2.4.3", "config": "^3.3.9", "cors": "^2.8.5", "escape-string-regexp": "^4.0.0", "express": "^4.18.2", "joi": "^18.0.0", "joi-objectid": "^4.0.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "mongoose": "5.13.21", "ms": "^2.1.3", "node-cron": "^4.2.1", "nodemailer": "^6.9.5", "redis": "2.8.0", "request": "2.88.2", "request-promise": "4.2.6", "rr": "^0.1.0", "socket.io": "^4.7.2", "uuid": "^11.1.0", "validator": "^13.15.15", "winston": "2.4.6", "winston-daily-rotate-file": "3.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}}