const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model quản lý văn bản trong hệ thống
 * Bao gồm văn bản đến, văn bản đi và văn bản trả lời
 */
const DocumentSchema = new mongoose.Schema(
  {
    // Thông tin cơ bản
    title: {
      type: String,
      required: true
    },
    documentNumber: {
      type: String, // Số văn bản
      required: true,
      index: true
    },
    description: {
      type: String
    },

    // Loại văn bản theo hướng
    direction: {
      type: String,
      enum: ['incoming', 'outgoing', 'reply'],
      required: true,
      index: true
    },

    // Loại văn bản theo nội dung
    documentType: {
      type: String,
      enum: ['official', 'notification', 'report', 'request', 'decision', 'instruction'],
      default: 'official'
    },

    // Độ ưu tiên
    priority: {
      type: String,
      enum: ['low', 'normal', 'high', 'urgent'],
      default: 'normal'
    },

    // Trạng thái xử lý
    status: {
      type: String,
      enum: ['draft', 'pending', 'processing', 'completed', 'cancelled'],
      default: 'pending'
    },

    // Thông tin người gửi (cho văn bản đến)
    sender: {
      organization: String, // Tên cơ quan gửi
      department: String,   // Phòng ban
      contact: String,      // Thông tin liên hệ
      address: String       // Địa chỉ
    },

    // Thông tin người nhận (cho văn bản đi)
    recipient: {
      organization: String, // Tên cơ quan nhận
      department: String,   // Phòng ban
      contact: String,      // Thông tin liên hệ
      address: String       // Địa chỉ
    },

    // Văn bản gốc (cho văn bản trả lời)
    originalDocument: {
      type: Schema.Types.ObjectId,
      ref: 'Document'
    },

    // Người tạo/xử lý văn bản
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Người được giao xử lý
    assignedTo: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },

    // Đơn vị xử lý
    unit: {
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    },

    // Ngày văn bản
    documentDate: {
      type: String, // Format: DD-MM-YYYY
      validate: {
        validator: function(v) {
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: 'Ngày văn bản phải có định dạng DD-MM-YYYY'
      }
    },

    // Hạn xử lý
    deadline: {
      type: String, // Format: DD-MM-YYYY
      validate: {
        validator: function(v) {
          return !v || /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: 'Hạn xử lý phải có định dạng DD-MM-YYYY'
      }
    },

    // File đính kèm
    attachments: [{
      filename: String,
      originalName: String,
      path: String,
      size: Number,
      mimeType: String,
      uploadedAt: {
        type: Number,
        default: Date.now
      }
    }],

    // Ghi chú xử lý
    processingNotes: [{
      note: String,
      createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
      },
      createdAt: {
        type: Number,
        default: Date.now
      }
    }],

    // Tags để phân loại
    tags: [String],

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    deletedAt: {
      type: Number
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
DocumentSchema.index({ direction: 1, createdAt: -1 })
DocumentSchema.index({ status: 1, createdAt: -1 })
DocumentSchema.index({ documentType: 1, createdAt: -1 })
DocumentSchema.index({ createdBy: 1, createdAt: -1 })
DocumentSchema.index({ assignedTo: 1, createdAt: -1 })
DocumentSchema.index({ unit: 1, createdAt: -1 })
DocumentSchema.index({ documentNumber: 1 }, { unique: true })
DocumentSchema.index({ originalDocument: 1 })
DocumentSchema.index({ deadline: 1, status: 1 })

// Compound indexes
DocumentSchema.index({ direction: 1, status: 1, createdAt: -1 })
DocumentSchema.index({ unit: 1, direction: 1, createdAt: -1 })

module.exports = mongoConnections("master").model("Document", DocumentSchema)
