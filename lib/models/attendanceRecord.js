const mongoose = require('mongoose');
const mongoConnections = require("../connections/mongo")
const Schema = mongoose.Schema

/**
 * Model ghi nhận điểm danh của cán bộ
 * Lưu trữ thông tin điểm danh thực tế và trạng thái
 */
const AttendanceRecordSchema = new mongoose.Schema(
  {
    // Cán bộ điểm danh
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },

    // Liên kết với lịch làm việc
    schedule: {
      type: Schema.Types.ObjectId,
      ref: 'WorkSchedule',
      required: true
    },

    // Ngày điểm danh (format: DD-MM-YYYY)
    date: {
      type: String,
      required: true,
      validate: {
        validator: function(v) {
          // Kiểm tra định dạng DD-MM-YYYY
          return /^\d{2}-\d{2}-\d{4}$/.test(v);
        },
        message: '<PERSON><PERSON><PERSON> phải có định dạng DD-MM-YYYY'
      }
    },

    // Ca làm việc
    shift: {
      type: String,
      enum: ['morning', 'afternoon'],
      required: true
    },

    // Thời gian điểm danh thực tế (timestamp)
    checkinTime: {
      type: Number,
      required: true // Timestamp
    },

    // Trạng thái điểm danh
    status: {
      type: String,
      enum: ['on_time', 'late'],
      required: true
    },

    // Vị trí điểm danh (optional)
    location: {
      lat: {
        type: Number
      },
      lng: {
        type: Number
      },
      address: {
        type: String
      }
    },

    // Metadata
    createdAt: {
      type: Number,
      default: Date.now
    },
    updatedAt: {
      type: Number,
      default: Date.now
    }
  },
  { id: false, versionKey: false }
)

// Indexes cho performance
AttendanceRecordSchema.index({ user: 1, date: 1 }) // Tìm điểm danh theo user và ngày
AttendanceRecordSchema.index({ user: 1, createdAt: -1 }) // Lịch sử điểm danh của user
AttendanceRecordSchema.index({ date: 1, shift: 1 }) // Thống kê theo ngày và ca
AttendanceRecordSchema.index({ schedule: 1 }) // Liên kết với lịch làm việc
AttendanceRecordSchema.index({ status: 1, date: 1 }) // Thống kê theo trạng thái

// Compound index để tránh duplicate attendance
AttendanceRecordSchema.index({ user: 1, date: 1, shift: 1 }, { unique: true })

module.exports = mongoConnections("master").model("AttendanceRecord", AttendanceRecordSchema)