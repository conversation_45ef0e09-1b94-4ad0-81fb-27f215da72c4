/**
 * Helper utility cho notification
 * Tích hợp notification vào các services khác
 */

const notificationService = require('../services/notificationService');

class NotificationHelper {
  /**
   * Tự động lên lịch thông báo khi tạo lịch làm việc
   * @param {Array} schedules - <PERSON>h sách lịch làm việc mới tạo
   */
  static async scheduleForNewWorkSchedules(schedules) {
    try {
      if (!schedules || schedules.length === 0) return;

      const scheduleIds = schedules.map(s => s._id || s.id);
      await notificationService.autoScheduleForNewWorkSchedules(scheduleIds);

    } catch (error) {
      console.error('Error scheduling notifications for new work schedules:', error);
    }
  }

  /**
   * G<PERSON>i thông báo khi đơn xin nghỉ được duyệt
   * @param {Object} leaveRequest - Đơn xin nghỉ
   */
  static async notifyLeaveRequestApproval(leaveRequest) {
    try {
      await notificationService.sendLeaveRequestNotification(leaveRequest);
    } catch (error) {
      console.error('Error sending leave request notification:', error);
    }
  }

  /**
   * Gửi thông báo điểm danh thành công
   * @param {String} userId - ID cán bộ
   * @param {Object} attendanceData - Dữ liệu điểm danh
   */
  static async notifySuccessfulCheckin(userId, attendanceData) {
    try {
      const statusText = attendanceData.status === 'on_time' ? 'đúng giờ' : 'muộn';
      const shiftText = attendanceData.shift === 'morning' ? 'sáng' : 'chiều';

      await notificationService.sendPushNotification(
        userId,
        'Điểm danh thành công',
        `Bạn đã điểm danh ${statusText} cho ca ${shiftText} lúc ${new Date(attendanceData.checkinTime).toLocaleTimeString('vi-VN')}`,
        {
          type: 'checkin_success',
          status: attendanceData.status,
          shift: attendanceData.shift,
          checkinTime: attendanceData.checkinTime
        }
      );
    } catch (error) {
      console.error('Error sending checkin notification:', error);
    }
  }

  /**
   * Gửi thông báo nhắc nhở điểm danh cho những người chưa điểm danh
   * @param {Array} userIds - Danh sách ID cán bộ chưa điểm danh
   * @param {String} shift - Ca làm việc
   */
  static async notifyMissedCheckin(userIds, shift) {
    try {
      const shiftText = shift === 'morning' ? 'sáng' : 'chiều';
      const message = `Bạn chưa điểm danh cho ca ${shiftText} hôm nay. Vui lòng điểm danh ngay!`;

      for (const userId of userIds) {
        await notificationService.sendPushNotification(
          userId,
          'Nhắc nhở điểm danh',
          message,
          {
            type: 'missed_checkin_reminder',
            shift: shift,
            date: new Date().toISOString().split('T')[0]
          }
        );
      }
    } catch (error) {
      console.error('Error sending missed checkin notifications:', error);
    }
  }

  /**
   * Gửi thông báo tóm tắt cuối ngày
   * @param {String} userId - ID cán bộ
   * @param {Object} dailySummary - Tóm tắt ngày làm việc
   */
  static async sendDailySummary(userId, dailySummary) {
    try {
      const { totalShifts, completedShifts, missedShifts, date } = dailySummary;

      let message = `Tóm tắt ngày ${date}:\n`;
      message += `- Tổng ca làm việc: ${totalShifts}\n`;
      message += `- Đã điểm danh: ${completedShifts}\n`;
      message += `- Vắng mặt: ${missedShifts}`;

      await notificationService.sendPushNotification(
        userId,
        'Tóm tắt ngày làm việc',
        message,
        {
          type: 'daily_summary',
          date: date,
          summary: dailySummary
        }
      );
    } catch (error) {
      console.error('Error sending daily summary:', error);
    }
  }
}

module.exports = NotificationHelper;