/**
 * Service xử lý logic lịch làm việc
 * <PERSON>u<PERSON>n lý tạo, c<PERSON><PERSON>, x<PERSON><PERSON> lịch làm việc cho cán bộ
 */

const _ = require('lodash');
const moment = require('moment');
const WorkSchedule = require('../models/workSchedule');
const User = require('../models/user');
const attendancePermission = require('../util/attendancePermission');
const NotificationHelper = require('../util/notificationHelper');
const DateUtils = require('../utils/dateUtils');
const attendanceCache = require('../util/attendanceCache');

class ScheduleService {
  /**
   * Tạo lịch làm việc cho cán bộ
   * @param {String} creatorId - ID người tạo lịch
   * @param {Array} userIds - Danh sách ID cán bộ
   * @param {Object} dateRange - <PERSON><PERSON><PERSON><PERSON> thời gian { startDate, endDate }
   * @param {Array} shifts - <PERSON>h sách ca làm việc ['morning', 'afternoon']
   * @returns {Object} Kết quả tạo lịch
   */
  async createWorkSchedule(creatorId, userIds, dateRange, shifts) {
    try {
      // Kiểm tra quyền tạo lịch
      const permissionCheck = await attendancePermission.checkSchedulePermission(creatorId, userIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;
      const { startDate, endDate } = dateRange;

      // Validate shifts
      const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
      if (validShifts.length === 0) {
        return {
          success: false,
          message: {
            head: 'Lỗi ca làm việc',
            body: 'Phải chọn ít nhất một ca làm việc'
          },
          data: null
        };
      }

      // Validate định dạng startDate và endDate
      if (!DateUtils.isValidDDMMYYYY(startDate)) {
        return {
          success: false,
          message: {
            head: 'Lỗi định dạng ngày',
            body: 'startDate phải có định dạng DD-MM-YYYY'
          },
          data: null
        };
      }

      if (!DateUtils.isValidDDMMYYYY(endDate)) {
        return {
          success: false,
          message: {
            head: 'Lỗi định dạng ngày',
            body: 'endDate phải có định dạng DD-MM-YYYY'
          },
          data: null
        };
      }

      // Tạo danh sách ngày làm việc (định dạng DD-MM-YYYY)
      const workDates = DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
      const createdSchedules = [];
      const errors = [];

      // Tạo lịch cho từng user và từng ngày
      for (const userId of allowedUserIds) {
        for (const date of workDates) {
          try {
            // Kiểm tra lịch đã tồn tại
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: date,
              status: 1
            });

            if (existingSchedule) {
              // Cập nhật lịch hiện có
              const updatedShifts = this.mergeShifts(existingSchedule.shifts, validShifts);
              existingSchedule.shifts = updatedShifts;
              existingSchedule.updatedAt = Date.now();
              await existingSchedule.save();
              createdSchedules.push(existingSchedule);
            } else {
              // Tạo lịch mới
              const scheduleData = {
                user: userId,
                date: date,
                shifts: validShifts.map(shiftType => ({
                  type: shiftType,
                  startTime: shiftType === 'morning' ? '08:00' : '14:00',
                  status: 'scheduled'
                })),
                createdBy: creatorId,
                status: 1
              };

              const newSchedule = await WorkSchedule.create(scheduleData);
              createdSchedules.push(newSchedule);
            }
          } catch (error) {
            errors.push({
              userId,
              date,
              error: error.message
            });
          }
        }
      }

      // Tự động lên lịch thông báo cho các lịch làm việc mới tạo
      if (createdSchedules.length > 0) {
        NotificationHelper.scheduleForNewWorkSchedules(createdSchedules);

        // Invalidate cache cho các user có lịch mới
        const affectedUserIds = [...new Set(createdSchedules.map(s => s.user))];
        for (const userId of affectedUserIds) {
          attendanceCache.invalidateUserSchedule(userId);
        }
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: `Đã tạo lịch làm việc cho ${createdSchedules.length} ca làm việc`
        },
        data: {
          created: createdSchedules.length,
          errors: errors,
          schedules: createdSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo lịch làm việc linh hoạt (khác nhau theo từng ngày)
   * @param {String} creatorId - ID người tạo lịch
   * @param {Array} schedules - Mảng lịch: [{ date, userIds, shifts }]
   * @returns {Object} Kết quả tạo lịch
   */
  async createFlexibleWorkSchedule(creatorId, schedules) {
    try {
      const createdSchedules = [];
      const errors = [];

      // Kiểm tra duplicate dates trong input
      const dateCount = {};
      for (const schedule of schedules) {
        const { date } = schedule;
        if (dateCount[date]) {
          return {
            success: false,
            message: {
              head: 'Lỗi dữ liệu đầu vào',
              body: `Ngày ${date} bị trùng lặp trong danh sách lịch. Mỗi ngày chỉ được xuất hiện một lần.`
            },
            data: null
          };
        }
        dateCount[date] = true;
      }

      const allUserIds = [...new Set(schedules.flatMap(s => s.userIds))];

      // Kiểm tra quyền tạo lịch cho tất cả users
      const permissionCheck = await attendancePermission.checkSchedulePermission(creatorId, allUserIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;

      // Tạo lịch cho từng ngày
      for (const daySchedule of schedules) {
        const { date, userIds, shifts } = daySchedule;

        // Lọc chỉ những user được phép
        const validUserIds = userIds.filter(userId => allowedUserIds.includes(userId));

        if (validUserIds.length === 0) {
          errors.push({
            date,
            error: 'Không có user nào được phép tạo lịch'
          });
          continue;
        }

        // Validate shifts
        const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
        if (validShifts.length === 0) {
          errors.push({
            date,
            error: 'Không có ca làm việc hợp lệ'
          });
          continue;
        }

        // Tạo lịch cho từng user trong ngày này
        for (const userId of validUserIds) {
          try {
            // Kiểm tra lịch đã tồn tại
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: date,
              status: 1
            });

            if (existingSchedule) {
              // Cập nhật lịch hiện có
              const updatedShifts = this.mergeShifts(existingSchedule.shifts, validShifts);
              existingSchedule.shifts = updatedShifts;
              existingSchedule.updatedAt = Date.now();
              await existingSchedule.save();
              createdSchedules.push(existingSchedule);
            } else {
              // Tạo lịch mới
              const scheduleData = {
                user: userId,
                date: date,
                shifts: validShifts.map(shiftType => ({
                  type: shiftType,
                  startTime: shiftType === 'morning' ? '08:00' : '14:00',
                  status: 'scheduled'
                })),
                createdBy: creatorId,
                status: 1
              };

              const newSchedule = await WorkSchedule.create(scheduleData);
              createdSchedules.push(newSchedule);
            }
          } catch (error) {
            errors.push({
              date,
              userId,
              error: error.message
            });
          }
        }
      }

      // Tự động lên lịch thông báo cho các lịch làm việc mới tạo
      if (createdSchedules.length > 0) {
        NotificationHelper.scheduleForNewWorkSchedules(createdSchedules);

        // Invalidate cache cho các user có lịch mới
        const affectedUserIds = [...new Set(createdSchedules.map(s => s.user))];
        for (const userId of affectedUserIds) {
          attendanceCache.invalidateUserSchedule(userId);
        }
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: `Đã tạo lịch làm việc cho ${createdSchedules.length} ca làm việc`
        },
        data: {
          created: createdSchedules.length,
          errors: errors,
          schedules: createdSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy lịch làm việc của cán bộ
   * @param {String} userId - ID cán bộ
   * @param {String} startDate - Ngày bắt đầu (YYYY-MM-DD)
   * @param {String} endDate - Ngày kết thúc (YYYY-MM-DD)
   * @returns {Array} Danh sách lịch làm việc
   */
  async getUserSchedule(userId, startDate, endDate) {
    try {
      // Kiểm tra cache trước
      if (startDate && endDate) {
        const cached = await attendanceCache.getUserSchedule(userId, startDate, endDate);
        if (cached) {
          return {
            success: true,
            message: {
              head: 'Thành công',
              body: 'Lấy lịch làm việc thành công'
            },
            data: cached
          };
        }
      }

      const query = {
        user: userId,
        status: 1
      };

      if (startDate && endDate) {
        query.date = {
          $gte: startDate,
          $lte: endDate
        };
      } else if (startDate) {
        query.date = { $gte: startDate };
      } else if (endDate) {
        query.date = { $lte: endDate };
      }

      const schedules = await WorkSchedule.find(query)
        .populate('user', 'name idNumber')
        .populate('createdBy', 'name')
        .sort({ date: 1 })
        .lean();

      // Cache kết quả nếu có startDate và endDate
      if (startDate && endDate) {
        attendanceCache.cacheUserSchedule(userId, startDate, endDate, schedules);
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy lịch làm việc thành công'
        },
        data: schedules
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật lịch làm việc
   * @param {String} scheduleId - ID lịch làm việc
   * @param {Object} updates - Dữ liệu cập nhật
   * @returns {Object} Kết quả cập nhật
   */
  async updateWorkSchedule(scheduleId, updates) {
    try {
      const schedule = await WorkSchedule.findById(scheduleId);

      if (!schedule) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy lịch làm việc'
          },
          data: null
        };
      }

      // Cập nhật các trường được phép
      const allowedFields = ['shifts', 'status'];
      const updateData = _.pick(updates, allowedFields);
      updateData.updatedAt = Date.now();

      const updatedSchedule = await WorkSchedule.findByIdAndUpdate(
        scheduleId,
        updateData,
        { new: true }
      ).populate('user', 'name idNumber');

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Cập nhật lịch làm việc thành công'
        },
        data: updatedSchedule
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật nhiều lịch làm việc cùng lúc
   * @param {Array} scheduleIds - Danh sách ID lịch làm việc
   * @param {Object} updates - Dữ liệu cập nhật
   * @returns {Object} Kết quả cập nhật
   */
  async updateMultipleWorkSchedules(scheduleIds, updates) {
    try {
      const results = [];
      const errors = [];

      for (const scheduleId of scheduleIds) {
        try {
          const result = await this.updateWorkSchedule(scheduleId, updates);
          if (result.success) {
            results.push(result.data);
          } else {
            errors.push({
              scheduleId,
              error: result.message.body
            });
          }
        } catch (error) {
          errors.push({
            scheduleId,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: {
          head: 'Cập nhật hoàn tất',
          body: `Đã cập nhật ${results.length}/${scheduleIds.length} lịch làm việc`
        },
        data: {
          updated: results.length,
          total: scheduleIds.length,
          errors: errors,
          schedules: results
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Xóa lịch làm việc (soft delete)
   * @param {String} scheduleId - ID lịch làm việc
   * @returns {Object} Kết quả xóa
   */
  async deleteWorkSchedule(scheduleId) {
    try {
      const schedule = await WorkSchedule.findByIdAndUpdate(
        scheduleId,
        {
          status: 0,
          updatedAt: Date.now()
        },
        { new: true }
      );

      if (!schedule) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy',
            body: 'Không tìm thấy lịch làm việc'
          },
          data: null
        };
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Xóa lịch làm việc thành công'
        },
        data: schedule
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách lịch làm việc được phân trang theo users (cho admin)
   * @param {String} viewerId - ID người xem
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Danh sách lịch làm việc nhóm theo user
   */
  async getScheduleListByUsers(viewerId, filters = {}) {
    try {
      // Lấy danh sách user có thể quản lý
      // let managedUserIds = await attendancePermission.getManagedUsers(viewerId);
      let managedUserIds = [];

      // Lọc theo đơn vị nếu có
      if (filters.unitId) {
        const unitUsers = await User.find({
          units: filters.unitId,
          status: 1
        }).select('_id').lean();
        const unitUserIds = unitUsers.map(u => u._id.toString());
        managedUserIds = managedUserIds.filter(id => unitUserIds.includes(id));
      }

      // Lọc theo userId cụ thể nếu có
      if (filters.userId) {
        managedUserIds = managedUserIds.filter(id => id === filters.userId);
      }

      // Tạo query cơ bản cho schedules
      const scheduleQuery = {
        status: 1
      };

      if (managedUserIds.length) {
        scheduleQuery.user = { $in: managedUserIds };
      }

      // Áp dụng filters theo ngày (định dạng DD-MM-YYYY)
      if (filters.startDate && filters.endDate) {
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, filters.endDate);
        scheduleQuery.date = { $in: dateRange };
      } else if (filters.startDate) {
        const startDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.startDate);
        const currentDateYYYYMMDD = DateUtils.getCurrentDateYYYYMMDD();
        const endDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDateYYYYMMDD);
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, endDateDDMMYYYY);
        scheduleQuery.date = { $in: dateRange };
      } else if (filters.endDate) {
        const endDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.endDate);
        const currentDateYYYYMMDD = DateUtils.getCurrentDateYYYYMMDD();
        const startDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDateYYYYMMDD);
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(startDateDDMMYYYY, filters.endDate);
        scheduleQuery.date = { $in: dateRange };
      }

      // Lấy danh sách users có schedules (distinct users)
      const usersWithSchedules = await WorkSchedule.distinct('user', scheduleQuery);

      // Populate thông tin user và sắp xếp theo tên
      const users = await User.find({
        _id: { $in: usersWithSchedules },
        status: 1
      })
      .select('name idNumber units')
      .populate('units', 'name')
      .sort({ name: 1 }) // Sắp xếp theo tên alphabetical
      .lean();

      // Tính toán pagination cho users
      const page = parseInt(filters.page) || 1;
      const limit = parseInt(filters.limit) || 20;
      const totalUsers = users.length;
      const totalPages = Math.ceil(totalUsers / limit);
      const skip = (page - 1) * limit;

      // Lấy users cho trang hiện tại
      const usersForCurrentPage = users.slice(skip, skip + limit);
      const userIdsForCurrentPage = usersForCurrentPage.map(u => u._id.toString());

      // Lấy tất cả schedules của users trong trang hiện tại
      const schedulesQuery = {
        ...scheduleQuery,
        user: { $in: userIdsForCurrentPage }
      };

      const [schedules, totalSchedules] = await Promise.all([
        WorkSchedule.find(schedulesQuery)
          .populate('user', 'name idNumber units')
          .populate('createdBy', 'name')
          .sort({ user: 1, date: -1 }) // Sắp xếp theo user, sau đó theo ngày (mới nhất trước)
          .lean(),
        WorkSchedule.countDocuments(scheduleQuery) // Tổng số schedules (tất cả users)
      ]);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách lịch làm việc theo user thành công'
        },
        data: {
          schedules,
          pagination: {
            page,
            limit,
            total: totalSchedules, // Tổng số schedules
            totalUsers: totalUsers, // Tổng số users có schedules
            pages: totalPages,
            usersInCurrentPage: usersForCurrentPage.length
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Lấy danh sách lịch làm việc (cho admin) - Method cũ, giữ lại để backward compatibility
   * @param {String} viewerId - ID người xem
   * @param {Object} filters - Bộ lọc
   * @returns {Object} Danh sách lịch làm việc
   */
  async getScheduleList(viewerId, filters = {}) {
    try {
      // Lấy danh sách user có thể quản lý
      let managedUserIds = await attendancePermission.getManagedUsers(viewerId);

      // Lọc theo đơn vị nếu có
      if (filters.unitId) {
        const unitUsers = await User.find({
          units: filters.unitId,
          status: 1
        }).select('_id').lean();
        const unitUserIds = unitUsers.map(u => u._id.toString());
        managedUserIds = managedUserIds.filter(id => unitUserIds.includes(id));
      }

      const query = {
        user: { $in: managedUserIds },
        status: 1
      };

      // Áp dụng filters (định dạng DD-MM-YYYY)
      if (filters.startDate && filters.endDate) {
        // Tạo range query cho date với định dạng DD-MM-YYYY
        // Sử dụng string comparison vì định dạng DD-MM-YYYY không sort được trực tiếp
        // Cần convert sang YYYY-MM-DD để so sánh hoặc dùng regex

        // Tạo danh sách tất cả ngày trong khoảng
        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, filters.endDate);
        query.date = { $in: dateRange };
      } else if (filters.startDate) {
        // Chỉ có startDate - lấy từ ngày đó trở đi
        const startDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.startDate);
        const currentDateYYYYMMDD = DateUtils.getCurrentDateYYYYMMDD();
        const endDateDDMMYYYY = DateUtils.convertYYYYMMDDtoDDMMYYYY(currentDateYYYYMMDD);

        const dateRange = DateUtils.generateDateRangeDDMMYYYY(filters.startDate, endDateDDMMYYYY);
        query.date = { $in: dateRange };
      } else if (filters.endDate) {
        // Chỉ có endDate - lấy đến ngày đó
        const endDateYYYYMMDD = DateUtils.convertDDMMYYYYtoYYYYMMDD(filters.endDate);
        const startDate = '01-01-2020'; // Ngày xa trong quá khứ

        const dateRange = DateUtils.generateDateRangeDDMMYYYY(startDate, filters.endDate);
        query.date = { $in: dateRange };
      }

      if (filters.userId) {
        query.user = filters.userId;
      }

      const page = parseInt(filters.page) || 1;
      const limit = parseInt(filters.limit) || 20;
      const skip = (page - 1) * limit;

      const [schedules, total] = await Promise.all([
        WorkSchedule.find(query)
          .populate('user', 'name idNumber units')
          .populate('createdBy', 'name')
          .sort({ date: -1, createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        WorkSchedule.countDocuments(query)
      ]);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách lịch làm việc thành công'
        },
        data: {
          schedules,
          pagination: {
            page,
            limit,
            total,
            pages: Math.ceil(total / limit)
          }
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Tạo danh sách ngày từ startDate đến endDate
   * @param {String} startDate - Ngày bắt đầu (DD-MM-YYYY)
   * @param {String} endDate - Ngày kết thúc (DD-MM-YYYY)
   * @returns {Array} Danh sách ngày (DD-MM-YYYY)
   * @deprecated Sử dụng DateUtils.generateDateRangeDDMMYYYY thay thế
   */
  generateDateRange(startDate, endDate) {
    // Sử dụng trực tiếp DateUtils
    return DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
  }

  /**
   * Merge shifts mới với shifts hiện có
   * @param {Array} existingShifts - Shifts hiện có
   * @param {Array} newShifts - Shifts mới
   * @returns {Array} Shifts đã merge
   */
  mergeShifts(existingShifts, newShifts) {
    const shiftMap = {};

    // Giữ lại shifts hiện có
    existingShifts.forEach(shift => {
      shiftMap[shift.type] = shift;
    });

    // Thêm shifts mới
    newShifts.forEach(shiftType => {
      if (!shiftMap[shiftType]) {
        shiftMap[shiftType] = {
          type: shiftType,
          startTime: shiftType === 'morning' ? '08:00' : '14:00',
          status: 'scheduled'
        };
      }
    });

    return Object.values(shiftMap);
  }

  /**
   * Tạo lịch làm việc tự động cho tuần tiếp theo
   * Chạy vào 22h thứ 6 hàng tuần
   * @returns {Object} Kết quả tạo lịch tự động
   */
  async createAutoWeeklySchedule() {
    try {
      // Tính toán tuần tiếp theo (từ thứ 2 đến chủ nhật)
      const nextMonday = moment().add(1, 'week').startOf('isoWeek');
      const nextSunday = moment().add(1, 'week').endOf('isoWeek');

      const startDate = nextMonday.format('YYYY-MM-DD');
      const endDate = nextSunday.format('YYYY-MM-DD');

      // Lấy tất cả cán bộ active
      const allUsers = await User.find({ status: 1 }).select('_id').lean();
      const userIds = allUsers.map(user => user._id.toString());

      if (userIds.length === 0) {
        return {
          success: false,
          message: {
            head: 'Không có dữ liệu',
            body: 'Không tìm thấy cán bộ nào để tạo lịch'
          },
          data: null
        };
      }

      // Tạo lịch cho tất cả cán bộ làm cả 2 ca
      const result = await this.createWorkSchedule(
        'SYSTEM', // System tạo tự động
        userIds,
        { startDate, endDate },
        ['morning', 'afternoon']
      );

      return {
        success: result.success,
        message: {
          head: 'Tạo lịch tự động',
          body: `Đã tạo lịch tự động cho tuần ${startDate} đến ${endDate}: ${result.data?.created || 0} ca làm việc`
        },
        data: {
          ...result.data,
          period: { startDate, endDate },
          totalUsers: userIds.length
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật lịch làm việc linh hoạt (khác nhau theo từng ngày)
   * @param {String} updaterId - ID người cập nhật
   * @param {Array} schedules - Mảng lịch: [{ date, userIds, shifts }]
   * @returns {Object} Kết quả cập nhật lịch
   */
  async updateFlexibleWorkSchedule(updaterId, schedules) {
    try {
      const updatedSchedules = [];
      const errors = [];
      const allUserIds = [...new Set(schedules.flatMap(s => s.userIds))];

      // Kiểm tra quyền cập nhật lịch cho tất cả users
      const permissionCheck = await attendancePermission.checkSchedulePermission(updaterId, allUserIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers;

      // Cập nhật lịch cho từng ngày
      for (const daySchedule of schedules) {
        const { date, userIds, shifts } = daySchedule;

        // Validate định dạng date DD-MM-YYYY
        if (!DateUtils.isValidDDMMYYYY(date)) {
          errors.push({
            date,
            error: 'Định dạng ngày không hợp lệ. Cần định dạng DD-MM-YYYY'
          });
          continue;
        }

        const dateDDMMYYYY = date;

        // Lọc chỉ những user được phép
        const validUserIds = userIds.filter(userId => allowedUserIds.includes(userId));

        if (validUserIds.length === 0) {
          errors.push({
            date,
            error: 'Không có user nào được phép cập nhật lịch'
          });
          continue;
        }

        // Validate shifts
        const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
        if (validShifts.length === 0) {
          errors.push({
            date,
            error: 'Không có ca làm việc hợp lệ'
          });
          continue;
        }

        // Cập nhật lịch cho từng user trong ngày này
        for (const userId of validUserIds) {
          try {
            // Tìm lịch hiện có (sử dụng định dạng DD-MM-YYYY)
            const existingSchedule = await WorkSchedule.findOne({
              user: userId,
              date: dateDDMMYYYY,
              status: 1
            });

            if (existingSchedule) {
              // Cập nhật lịch hiện có
              const newShifts = validShifts.map(shiftType => ({
                type: shiftType,
                startTime: shiftType === 'morning' ? '08:00' : '14:00',
                status: 'scheduled'
              }));

              existingSchedule.shifts = newShifts;
              existingSchedule.updatedAt = Date.now();
              await existingSchedule.save();
              updatedSchedules.push(existingSchedule);
            } else {
              // Tạo lịch mới nếu chưa có (sử dụng định dạng DD-MM-YYYY)
              const scheduleData = {
                user: userId,
                date: dateDDMMYYYY,
                shifts: validShifts.map(shiftType => ({
                  type: shiftType,
                  startTime: shiftType === 'morning' ? '08:00' : '14:00',
                  status: 'scheduled'
                })),
                createdBy: updaterId,
                status: 1
              };

              const newSchedule = await WorkSchedule.create(scheduleData);
              updatedSchedules.push(newSchedule);
            }

            // Invalidate cache
            attendanceCache.invalidateUserSchedule(userId);

          } catch (error) {
            errors.push({
              date: dateDDMMYYYY,
              userId,
              error: error.message
            });
          }
        }
      }

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: `Đã cập nhật lịch làm việc cho ${updatedSchedules.length} ca làm việc`
        },
        data: {
          updated: updatedSchedules.length,
          errors: errors,
          schedules: updatedSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }

  /**
   * Cập nhật lịch làm việc theo danh sách ID - Phiên bản đơn giản hóa
   * @param {String} updaterId - ID người cập nhật
   * @param {Array} schedules - Mảng lịch: [{ scheduleId, shifts }]
   * @returns {Object} Kết quả cập nhật lịch
   *
   * Mô tả:
   * - Chỉ cho phép cập nhật ca trực (shifts) của lịch làm việc
   * - Tự động cập nhật thời gian bắt đầu ca theo loại ca (morning: 08:00, afternoon: 14:00)
   * - Không thay đổi các thông tin khác như ngày, user, status
   * - Kiểm tra quyền cập nhật lịch của user
   */
  async updateWorkSchedulesByIds(updaterId, schedules) {
    try {
      const updatedSchedules = [];
      const errors = [];

      // Lấy danh sách tất cả scheduleId để kiểm tra quyền
      const scheduleIds = schedules.map(s => s.scheduleId);

      // Tìm tất cả lịch cần cập nhật
      const existingSchedules = await WorkSchedule.find({
        _id: { $in: scheduleIds },
        status: 1
      }).populate('user', 'name idNumber');

      if (existingSchedules.length === 0) {
        return {
          success: false,
          message: {
            head: 'Không tìm thấy lịch',
            body: 'Không tìm thấy lịch làm việc nào để cập nhật'
          },
          data: null
        };
      }

      // Kiểm tra quyền cập nhật cho tất cả user có lịch
      const affectedUserIds = [...new Set(existingSchedules.map(s => s.user._id.toString()))];
      const permissionCheck = await attendancePermission.checkSchedulePermission(updaterId, affectedUserIds);

      if (!permissionCheck.canCreate) {
        return {
          success: false,
          message: {
            head: 'Không có quyền',
            body: permissionCheck.message
          },
          data: null
        };
      }

      const allowedUserIds = permissionCheck.allowedUsers.map(id => id.toString());

      // Xử lý từng lịch cần cập nhật
      for (const scheduleUpdate of schedules) {
        const { scheduleId, shifts } = scheduleUpdate;

        try {
          // Tìm lịch tương ứng
          const existingSchedule = existingSchedules.find(s => s._id.toString() === scheduleId);

          if (!existingSchedule) {
            errors.push({
              scheduleId,
              error: 'Không tìm thấy lịch làm việc'
            });
            continue;
          }

          // Kiểm tra quyền cập nhật lịch của user này
          if (!allowedUserIds.includes(existingSchedule.user._id.toString())) {
            errors.push({
              scheduleId,
              error: 'Không có quyền cập nhật lịch của user này'
            });
            continue;
          }

          // Validate shifts
          const validShifts = shifts.filter(shift => ['morning', 'afternoon'].includes(shift));
          if (validShifts.length === 0) {
            errors.push({
              scheduleId,
              error: 'Không có ca làm việc hợp lệ'
            });
            continue;
          }

          // Tạo danh sách ca trực mới với thời gian tự động
          const newShifts = validShifts.map(shiftType => ({
            type: shiftType,
            startTime: shiftType === 'morning' ? '08:00' : '14:00',
            status: 'scheduled' // Reset về trạng thái đã lên lịch
          }));

          // Cập nhật lịch
          existingSchedule.shifts = newShifts;
          existingSchedule.updatedAt = Date.now();
          await existingSchedule.save();

          updatedSchedules.push(existingSchedule);

          // Invalidate cache cho user này
          attendanceCache.invalidateUserSchedule(existingSchedule.user._id);

        } catch (error) {
          errors.push({
            scheduleId,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: {
          head: 'Cập nhật thành công',
          body: `Đã cập nhật ${updatedSchedules.length}/${schedules.length} lịch làm việc`
        },
        data: {
          updated: updatedSchedules.length,
          total: schedules.length,
          errors: errors,
          schedules: updatedSchedules
        }
      };

    } catch (error) {
      return {
        success: false
      };
    }
  }
}

module.exports = new ScheduleService();