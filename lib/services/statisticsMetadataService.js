const _ = require('lodash');
const moment = require('moment');
const redisConnection = require('../connections/redis');
const StatisticsUtils = require('../utils/statisticsUtils');
const statisticsService = require('./statisticsService');
const SocketNotifyManager = require('../socket/socketNotifyManager');

/**
 * Service quản lý metadata và cache cho hệ thống thống kê
 * Xử lý tính toán định kỳ, cache dữ liệu và real-time notifications
 */
class StatisticsMetadataService {
  constructor() {
    this.redis = redisConnection('master').getConnection();
    this.triggeredEvents = new Set(); // Lưu trữ các events cần xử lý
  }

  /**
   * Tính toán và cache tất cả metadata thống kê
   * Chạy định kỳ mỗi 10 phút
   */
  async calculateAllMetadata() {
    try {
      console.log(`[${new Date().toISOString()}] Starting statistics metadata calculation...`);

      const timeRanges = ['day', 'week', 'month'];
      const results = {};

      for (const timeRange of timeRanges) {
        try {
          // Tính toán cho từng loại thống kê
          const [
            onDutyOfficers,
            lateAttendance,
            officerSummary,
            officersByArea
          ] = await Promise.all([
            statisticsService.getOnDutyOfficers({ timeRange }),
            statisticsService.getLateAttendanceStats({ timeRange }),
            statisticsService.getOfficerSummaryStats({ timeRange }),
            statisticsService.getOfficersByAreaStats({ timeRange })
          ]);

          // Cache kết quả
          await this.cacheStatisticsData('on_duty_officers', timeRange, onDutyOfficers.data);
          await this.cacheStatisticsData('late_attendance', timeRange, lateAttendance.data);
          await this.cacheStatisticsData('officer_summary', timeRange, officerSummary.data);
          await this.cacheStatisticsData('officers_by_area', timeRange, officersByArea.data);

          results[timeRange] = {
            onDutyOfficers: onDutyOfficers.success,
            lateAttendance: lateAttendance.success,
            officerSummary: officerSummary.success,
            officersByArea: officersByArea.success
          };

        } catch (error) {
          console.error(`Error calculating metadata for ${timeRange}:`, error);
          results[timeRange] = { error: error.message };
        }
      }

      console.log(`[${new Date().toISOString()}] Statistics metadata calculation completed:`, results);
      return { success: true, results };

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Statistics metadata calculation failed:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Cache dữ liệu thống kê vào Redis
   * @param {String} type - Loại thống kê
   * @param {String} timeRange - Khoảng thời gian
   * @param {Object} data - Dữ liệu cần cache
   */
  async cacheStatisticsData(type, timeRange, data) {
    try {
      const today = moment().format('DD-MM-YYYY');
      const cacheKey = StatisticsUtils.generateCacheKey(type, timeRange, today);

      await this.redis.setex(
        cacheKey,
        global.config?.statistics?.cacheTTL || 300, // 5 phút
        JSON.stringify(data)
      );

      // Cache metadata summary
      const summaryKey = StatisticsUtils.generateCacheKey('summary', type, timeRange);
      const summary = this.extractSummary(type, data);

      await this.redis.setex(
        summaryKey,
        global.config?.statistics?.cacheTTL || 300,
        JSON.stringify(summary)
      );

    } catch (error) {
      console.error(`Error caching statistics data for ${type}:`, error);
    }
  }

  /**
   * Lấy dữ liệu thống kê từ cache
   * @param {String} type - Loại thống kê
   * @param {String} timeRange - Khoảng thời gian
   * @param {String} date - Ngày (DD-MM-YYYY)
   * @returns {Object|null} Dữ liệu từ cache hoặc null
   */
  async getCachedStatisticsData(type, timeRange, date = null) {
    try {
      const targetDate = date || moment().format('DD-MM-YYYY');
      const cacheKey = StatisticsUtils.generateCacheKey(type, timeRange, targetDate);

      const cachedData = await this.redis.get(cacheKey);
      return cachedData ? JSON.parse(cachedData) : null;

    } catch (error) {
      console.error(`Error getting cached statistics data for ${type}:`, error);
      return null;
    }
  }

  /**
   * Trích xuất summary từ dữ liệu thống kê
   * @param {String} type - Loại thống kê
   * @param {Object} data - Dữ liệu thống kê
   * @returns {Object} Summary data
   */
  extractSummary(type, data) {
    switch (type) {
      case 'on_duty_officers':
        return {
          totalOnDuty: data.summary?.totalOnDuty || 0,
          byDutyType: data.summary?.byDutyType || {},
          lastUpdated: Date.now()
        };

      case 'late_attendance':
        return {
          totalLate: data.summary?.totalLate || 0,
          lateRate: data.summary?.lateRate || 0,
          totalScheduled: data.summary?.totalScheduled || 0,
          lastUpdated: Date.now()
        };

      case 'officer_summary':
        return {
          totalOfficers: data.summary?.totalOfficers || 0,
          activeOfficers: data.summary?.activeOfficers || 0,
          workingOfficers: data.workStatus?.working || 0,
          lastUpdated: Date.now()
        };

      case 'officers_by_area':
        return {
          totalAreas: data.summary?.totalAreas || 0,
          totalOfficers: data.summary?.totalOfficers || 0,
          averageOfficersPerArea: data.summary?.averageOfficersPerArea || 0,
          lastUpdated: Date.now()
        };

      default:
        return { lastUpdated: Date.now() };
    }
  }

  /**
   * Đăng ký trigger event
   * @param {String} eventType - Loại event
   * @param {Object} eventData - Dữ liệu event
   */
  registerTriggerEvent(eventType, eventData = {}) {
    const event = {
      type: eventType,
      data: eventData,
      timestamp: Date.now()
    };

    this.triggeredEvents.add(JSON.stringify(event));
    console.log(`[${new Date().toISOString()}] Registered trigger event: ${eventType}`);
  }

  /**
   * Xử lý các trigger events đã đăng ký
   * Chạy định kỳ mỗi phút
   */
  async processTriggeredEvents() {
    if (this.triggeredEvents.size === 0) {
      return { success: true, processed: 0 };
    }

    try {
      console.log(`[${new Date().toISOString()}] Processing ${this.triggeredEvents.size} triggered events...`);

      const events = Array.from(this.triggeredEvents).map(eventStr => JSON.parse(eventStr));
      this.triggeredEvents.clear();

      // Nhóm events theo loại để xử lý hiệu quả
      const eventGroups = _.groupBy(events, 'type');

      const affectedTypes = new Set();

      // Xác định loại thống kê nào cần cập nhật
      Object.keys(eventGroups).forEach(eventType => {
        switch (eventType) {
          case 'duty_shift_updated':
          case 'duty_schedule_updated':
            affectedTypes.add('on_duty_officers');
            break;

          case 'attendance_updated':
          case 'work_schedule_updated':
            affectedTypes.add('late_attendance');
            affectedTypes.add('officer_summary');
            affectedTypes.add('officers_by_area');
            break;

          case 'user_updated':
          case 'leave_request_updated':
            affectedTypes.add('officer_summary');
            affectedTypes.add('officers_by_area');
            break;

          case 'area_updated':
            affectedTypes.add('officers_by_area');
            break;
        }
      });

      // Tính toán lại metadata cho các loại bị ảnh hưởng
      for (const type of affectedTypes) {
        await this.recalculateMetadataForType(type);
      }

      // Gửi real-time notifications
      await this.sendRealTimeNotifications(Array.from(affectedTypes), events);

      console.log(`[${new Date().toISOString()}] Processed triggered events. Affected types: ${Array.from(affectedTypes).join(', ')}`);

      return {
        success: true,
        processed: events.length,
        affectedTypes: Array.from(affectedTypes)
      };

    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error processing triggered events:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Tính toán lại metadata cho một loại thống kê cụ thể
   * @param {String} type - Loại thống kê
   */
  async recalculateMetadataForType(type) {
    try {
      const timeRanges = ['day', 'week', 'month'];

      for (const timeRange of timeRanges) {
        let result;

        switch (type) {
          case 'on_duty_officers':
            result = await statisticsService.getOnDutyOfficers({ timeRange });
            break;
          case 'late_attendance':
            result = await statisticsService.getLateAttendanceStats({ timeRange });
            break;
          case 'officer_summary':
            result = await statisticsService.getOfficerSummaryStats({ timeRange });
            break;
          case 'officers_by_area':
            result = await statisticsService.getOfficersByAreaStats({ timeRange });
            break;
        }

        if (result && result.success) {
          await this.cacheStatisticsData(type, timeRange, result.data);
        }
      }

    } catch (error) {
      console.error(`Error recalculating metadata for ${type}:`, error);
    }
  }

  /**
   * Gửi real-time notifications cho clients
   * @param {Array} affectedTypes - Các loại thống kê bị ảnh hưởng
   * @param {Array} events - Danh sách events
   */
  async sendRealTimeNotifications(affectedTypes, events) {
    try {
      // Gửi notification sử dụng socket manager mới
      await SocketNotifyManager.sendStatisticsUpdated(affectedTypes, events);

      console.log(`[${new Date().toISOString()}] Sent real-time notifications for: ${affectedTypes.join(', ')}`);

    } catch (error) {
      console.error('Error sending real-time notifications:', error);
    }
  }

  /**
   * Invalidate cache cho một loại thống kê
   * @param {String} type - Loại thống kê
   * @param {String} timeRange - Khoảng thời gian (optional)
   */
  async invalidateCache(type, timeRange = null) {
    try {
      const today = moment().format('DD-MM-YYYY');
      const timeRanges = timeRange ? [timeRange] : ['day', 'week', 'month'];

      for (const range of timeRanges) {
        const cacheKey = StatisticsUtils.generateCacheKey(type, range, today);
        const summaryKey = StatisticsUtils.generateCacheKey('summary', type, range);

        await Promise.all([
          this.redis.del(cacheKey),
          this.redis.del(summaryKey)
        ]);
      }

      console.log(`[${new Date().toISOString()}] Invalidated cache for ${type}`);

    } catch (error) {
      console.error(`Error invalidating cache for ${type}:`, error);
    }
  }
}

module.exports = new StatisticsMetadataService();
