const _ = require('lodash');
const moment = require('moment');
const StatisticsUtils = require('../utils/statisticsUtils');

// Models
const User = require('../models/user');
const DutyShift = require('../models/dutyShift');
const AttendanceRecord = require('../models/attendanceRecord');
const WorkSchedule = require('../models/workSchedule');
const LeaveRequest = require('../models/leaveRequest');
const Area = require('../models/area');
const Report = require('../models/report');
const Document = require('../models/document');

// Duty Schedule Models
const DutyMainSchedule = require('../models/dutyMainSchedule');
const DutySubSchedule = require('../models/dutySubSchedule');
const DutyLocationSchedule = require('../models/dutyLocationSchedule');
const DutyPatrolSchedule = require('../models/dutyPatrolSchedule');
const DutyStadiumSchedule = require('../models/dutyStadiumSchedule');
const DutyCriminalSchedule = require('../models/dutyCriminalSchedule');
const DutySpecializedSchedule = require('../models/dutySpecializedSchedule');

/**
 * Service xử lý logic thống kê cho hệ thống
 * Cung cấp các phương thức tính toán thống kê cho 4 API chính
 */
class StatisticsService {

  /**
   * Lấy danh sách cán bộ đang trực ban
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Danh sách cán bộ trực ban và thống kê
   */
  async getOnDutyOfficers(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      // Tính toán khoảng thời gian
      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);
      const currentTime = Date.now();

      // Lấy tất cả ca trực trong khoảng thời gian
      const dutyShifts = await this.getAllDutyShifts(period);

      // Lọc ra các ca trực đang diễn ra
      const onDutyShifts = dutyShifts.filter(shift => {
        return shift.startTime <= currentTime && shift.endTime >= currentTime && shift.status === 1;
      });

      // Populate thông tin cán bộ
      const populatedShifts = await this.populateOfficerInfo(onDutyShifts);

      // Tính toán thống kê
      const summary = this.calculateOnDutySummary(populatedShifts);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách cán bộ trực ban thành công'
        },
        data: {
          period,
          currentTime,
          officers: populatedShifts,
          summary
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy danh sách cán bộ trực ban'
        }
      };
    }
  }

  /**
   * Thống kê số cán bộ đi muộn
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ đi muộn
   */
  async getLateAttendanceStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu attendance và work schedule
      const [attendanceRecords, workSchedules] = await Promise.all([
        this.getAttendanceRecords(period),
        this.getWorkSchedules(period)
      ]);

      // Tính toán thống kê
      const summary = this.calculateLateAttendanceSummary(attendanceRecords, workSchedules);
      const byShift = this.calculateLateAttendanceByShift(attendanceRecords);
      const byUnit = await this.calculateLateAttendanceByUnit(attendanceRecords);
      const lateOfficers = await this.getLateOfficersDetail(attendanceRecords);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ đi muộn thành công'
        },
        data: {
          period,
          summary,
          byShift,
          byUnit,
          lateOfficers
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê cán bộ đi muộn'
        }
      };
    }
  }

  /**
   * Thống kê tổng số lượng cán bộ
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê tổng số lượng cán bộ
   */
  async getOfficerSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy tất cả dữ liệu cần thiết
      const [
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      ] = await Promise.all([
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period),
        this.getApprovedLeaveRequests(period)
      ]);

      // Tính toán thống kê tổng quan
      const summary = this.calculateOfficerSummary(allOfficers);
      const workStatus = this.calculateWorkStatus(
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      );
      const byUnit = await this.calculateOfficerSummaryByUnit(
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      );
      const attendanceRate = this.calculateAttendanceRate(workSchedules, attendanceRecords, byUnit);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng số lượng cán bộ thành công'
        },
        data: {
          period,
          summary,
          workStatus,
          byUnit,
          attendanceRate
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê tổng số lượng cán bộ'
        }
      };
    }
  }

  /**
   * Thống kê cán bộ theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ theo khu vực
   */
  async getOfficersByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const [
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      ] = await Promise.all([
        this.getAllAreas(),
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period)
      ]);

      // Tính toán thống kê theo khu vực
      const areaStats = await this.calculateOfficersByArea(
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      );

      const summary = this.calculateAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê cán bộ theo khu vực'
        }
      };
    }
  }

  /**
   * Thống kê số lượng báo cáo theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo khu vực
   */
  async getReportsByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const [
        areas,
        reports,
        allOfficers
      ] = await Promise.all([
        this.getAllAreas(),
        this.getReports(period),
        this.getAllOfficers()
      ]);

      // Tính toán thống kê báo cáo theo khu vực
      const areaStats = await this.calculateReportsByArea(areas, reports, allOfficers);
      const summary = this.calculateReportAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê báo cáo theo khu vực'
        }
      };
    }
  }

  /**
   * Thống kê tổng quan báo cáo
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê tổng quan báo cáo
   */
  async getReportsSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê tổng quan
      const summary = this.calculateReportsSummary(reports);
      const byStatus = this.calculateReportsByStatus(reports);
      const byReportType = this.calculateReportsByType(reports);
      const byJobType = this.calculateReportsByJobType(reports);
      const byUnit = await this.calculateReportsByUnit(reports);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng quan báo cáo thành công'
        },
        data: {
          period,
          summary,
          byStatus,
          byReportType,
          byJobType,
          byUnit
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê tổng quan báo cáo'
        }
      };
    }
  }

  /**
   * Thống kê báo cáo theo thời gian
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.groupBy - Nhóm theo: 'hour', 'day', 'week', 'month'
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo thời gian
   */
  async getReportsTimelineStats(params) {
    try {
      const { timeRange = 'week', startDate, endDate, groupBy = 'day', userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê theo thời gian
      const timeline = this.calculateReportsTimeline(reports, groupBy, period);
      const summary = this.calculateTimelineSummary(timeline);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo thời gian thành công'
        },
        data: {
          period,
          groupBy,
          timeline,
          summary
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê báo cáo theo thời gian'
        }
      };
    }
  }

  /**
   * Thống kê báo cáo theo trạng thái chi tiết
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê báo cáo theo trạng thái
   */
  async getReportsStatusStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const reports = await this.getReports(period);

      // Tính toán thống kê theo trạng thái
      const statusStats = this.calculateDetailedStatusStats(reports);
      const workflowStats = this.calculateWorkflowStats(reports);
      const processingTime = this.calculateProcessingTimeStats(reports);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê báo cáo theo trạng thái thành công'
        },
        data: {
          period,
          statusStats,
          workflowStats,
          processingTime
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê báo cáo theo trạng thái'
        }
      };
    }
  }

  /**
   * Thống kê tổng quan văn bản
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Thống kê tổng quan văn bản
   */
  async getDocumentsSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu văn bản
      const documents = await this.getDocuments(period);

      // Tính toán thống kê
      const summary = this.calculateDocumentsSummary(documents);
      const byDirection = this.calculateDocumentsByDirection(documents);
      const byStatus = this.calculateDocumentsByStatus(documents);
      const byType = this.calculateDocumentsByType(documents);
      const byUnit = this.calculateDocumentsByUnit(documents);
      const processingStats = this.calculateDocumentProcessingStats(documents);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng quan văn bản thành công'
        },
        data: {
          period,
          summary,
          byDirection,
          byStatus,
          byType,
          byUnit,
          processingStats
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê tổng quan văn bản'
        }
      };
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Lấy tất cả ca trực từ 8 loại lịch trực
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách tất cả ca trực
   */
  async getAllDutyShifts(period) {
    const dutyShifts = await DutyShift.find({
      startTime: { $gte: period.startTimestamp },
      endTime: { $lte: period.endTimestamp },
      status: { $ne: 2 } // Không lấy ca đã hủy
    })
    .populate('officer', 'name avatar idNumber units position areas')
    .populate('unit', 'name')
    .lean();

    return dutyShifts;
  }

  /**
   * Populate thông tin chi tiết cán bộ cho ca trực
   * @param {Array} dutyShifts - Danh sách ca trực
   * @returns {Array} Ca trực đã được populate
   */
  async populateOfficerInfo(dutyShifts) {
    return dutyShifts.map(shift => ({
      userId: shift.officer._id,
      name: shift.officer.name,
      avatar: shift.officer.avatar,
      idNumber: shift.officer.idNumber,
      unit: shift.unit,
      position: shift.officer.position,
      dutyInfo: {
        dutyType: this.getDutyType(shift),
        dutyName: shift.name,
        location: shift.locationDuty,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        forLeader: shift.forLeader,
        hasEquipment: shift.hasEquipment
      }
    }));
  }

  /**
   * Xác định loại trực từ ca trực
   * @param {Object} shift - Thông tin ca trực
   * @returns {String} Loại trực
   */
  getDutyType(shift) {
    // Logic xác định loại trực dựa trên source hoặc các field khác
    if (shift.source) {
      return shift.source;
    }

    // Fallback logic
    if (shift.locationDuty) return 'location';
    if (shift.forLeader) return 'main';
    return 'general';
  }

  /**
   * Tính toán thống kê tổng quan cho cán bộ trực ban
   * @param {Array} onDutyShifts - Danh sách ca trực đang diễn ra
   * @returns {Object} Thống kê tổng quan
   */
  calculateOnDutySummary(onDutyShifts) {
    const totalOnDuty = onDutyShifts.length;

    const byDutyType = _.countBy(onDutyShifts, 'dutyInfo.dutyType');
    const byStatus = _.countBy(onDutyShifts, 'dutyInfo.status');

    return {
      totalOnDuty,
      byDutyType,
      byStatus: {
        confirmed: byStatus[1] || 0,
        pending: byStatus[0] || 0,
        cancelled: byStatus[2] || 0
      }
    };
  }

  /**
   * Lấy dữ liệu attendance records
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách attendance records
   */
  async getAttendanceRecords(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await AttendanceRecord.find(dateQuery)
      .populate('user', 'name idNumber units position')
      .lean();
  }

  /**
   * Lấy dữ liệu work schedules
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách work schedules
   */
  async getWorkSchedules(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await WorkSchedule.find({
      ...dateQuery,
      status: 1
    })
    .populate('user', 'name idNumber units position')
    .lean();
  }

  /**
   * Lấy tất cả cán bộ
   * @returns {Array} Danh sách cán bộ
   */
  async getAllOfficers() {
    return await User.find({ status: 1 })
      .populate('units', 'name')
      .populate('position', 'name')
      .populate('areas', 'name level')
      .lean();
  }

  /**
   * Lấy tất cả khu vực
   * @returns {Array} Danh sách khu vực
   */
  async getAllAreas() {
    return await Area.find({ status: 1, level: 1 }) // Chỉ lấy level 1
      .lean();
  }

  /**
   * Lấy đơn xin nghỉ đã được duyệt
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách đơn xin nghỉ
   */
  async getApprovedLeaveRequests(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('startDate', period, 'ddmmyyyy');

    return await LeaveRequest.find({
      ...dateQuery,
      status: 'approved'
    })
    .populate('user', 'name idNumber')
    .lean();
  }

  /**
   * Lấy tất cả báo cáo trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách báo cáo
   */
  async getReports(period) {
    return await Report.find({
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    })
    .populate('createdBy', 'name idNumber units areas')
    .populate('jobType', 'name')
    .populate('unit', 'name')
    .populate('details.location.area', 'name level')
    .lean();
  }

  /**
   * Tính toán thống kê cán bộ đi muộn tổng quan
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @param {Array} workSchedules - Danh sách work schedules
   * @returns {Object} Thống kê tổng quan
   */
  calculateLateAttendanceSummary(attendanceRecords, workSchedules) {
    // Tính tổng số ca được lên lịch
    const totalScheduled = _.sumBy(workSchedules, schedule => schedule.shifts.length);

    // Đếm số ca đã điểm danh
    const totalCheckedIn = attendanceRecords.length;

    // Đếm số ca đi muộn
    const totalLate = attendanceRecords.filter(record => record.status === 'late').length;

    // Đếm số ca đúng giờ
    const totalOnTime = attendanceRecords.filter(record => record.status === 'on_time').length;

    // Tính tỷ lệ
    const lateRate = StatisticsUtils.calculateRate(totalLate, totalScheduled);
    const onTimeRate = StatisticsUtils.calculateRate(totalOnTime, totalScheduled);
    const absentRate = StatisticsUtils.calculateRate(totalScheduled - totalCheckedIn, totalScheduled);

    return {
      totalScheduled,
      totalCheckedIn,
      totalLate,
      lateRate,
      onTimeRate,
      absentRate
    };
  }

  /**
   * Tính toán thống kê cán bộ đi muộn theo ca
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @returns {Object} Thống kê theo ca
   */
  calculateLateAttendanceByShift(attendanceRecords) {
    const byShift = _.groupBy(attendanceRecords, 'shift');

    const result = {};
    ['morning', 'afternoon'].forEach(shift => {
      const shiftRecords = byShift[shift] || [];
      const scheduled = shiftRecords.length; // Tạm tính, cần cải thiện
      const checkedIn = shiftRecords.length;
      const late = shiftRecords.filter(r => r.status === 'late').length;

      result[shift] = {
        scheduled,
        checkedIn,
        late,
        lateRate: StatisticsUtils.calculateRate(late, scheduled)
      };
    });

    return result;
  }

  /**
   * Tính toán thống kê cán bộ đi muộn theo đơn vị
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @returns {Array} Thống kê theo đơn vị
   */
  async calculateLateAttendanceByUnit(attendanceRecords) {
    const byUnit = StatisticsUtils.groupByUnit(attendanceRecords, 'user.units.0');

    const result = [];
    for (const [unitId, records] of Object.entries(byUnit)) {
      if (unitId === 'unknown') continue;

      const scheduled = records.length; // Tạm tính
      const late = records.filter(r => r.status === 'late').length;
      const unitName = records[0]?.user?.units?.[0]?.name || 'Không xác định';

      result.push({
        unitId,
        unitName,
        scheduled,
        late,
        lateRate: StatisticsUtils.calculateRate(late, scheduled)
      });
    }

    return result;
  }

  /**
   * Lấy chi tiết cán bộ đi muộn
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @returns {Array} Chi tiết cán bộ đi muộn
   */
  async getLateOfficersDetail(attendanceRecords) {
    const lateRecords = attendanceRecords.filter(record => record.status === 'late');

    return lateRecords.map(record => {
      const scheduledTime = record.shift === 'morning' ? '08:00' : '14:00';
      const checkinTime = new Date(record.checkinTime);
      const scheduledDateTime = moment(record.date, 'DD-MM-YYYY')
        .set({
          hour: parseInt(scheduledTime.split(':')[0]),
          minute: parseInt(scheduledTime.split(':')[1])
        });

      const lateMinutes = Math.max(0, moment(checkinTime).diff(scheduledDateTime, 'minutes'));

      return {
        userId: record.user._id,
        name: record.user.name,
        idNumber: record.user.idNumber,
        unit: record.user.units?.[0]?.name || 'Không xác định',
        shift: record.shift,
        scheduledTime,
        checkinTime: record.checkinTime,
        lateMinutes
      };
    });
  }

  /**
   * Tính toán thống kê tổng quan cán bộ
   * @param {Array} allOfficers - Danh sách tất cả cán bộ
   * @returns {Object} Thống kê tổng quan
   */
  calculateOfficerSummary(allOfficers) {
    const totalOfficers = allOfficers.length;
    const activeOfficers = allOfficers.filter(officer => officer.status === 1).length;
    const inactiveOfficers = totalOfficers - activeOfficers;

    return {
      totalOfficers,
      activeOfficers,
      inactiveOfficers
    };
  }

  /**
   * Tính toán trạng thái làm việc của cán bộ
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @param {Array} leaveRequests - Đơn xin nghỉ
   * @returns {Object} Thống kê trạng thái làm việc
   */
  calculateWorkStatus(allOfficers, workSchedules, attendanceRecords, dutyShifts, leaveRequests) {
    const currentTime = Date.now();

    // Tạo map để tra cứu nhanh
    const attendanceMap = _.groupBy(attendanceRecords, 'user._id');
    const scheduleMap = _.groupBy(workSchedules, 'user');
    const dutyMap = _.groupBy(dutyShifts.filter(s =>
      s.startTime <= currentTime && s.endTime >= currentTime
    ), 'officer._id');
    const leaveMap = _.groupBy(leaveRequests, 'user._id');

    let working = 0, onDuty = 0, onLeave = 0, absent = 0, notScheduled = 0;

    allOfficers.forEach(officer => {
      const officerId = officer._id.toString();

      // Kiểm tra nghỉ phép
      if (leaveMap[officerId]) {
        onLeave++;
        return;
      }

      // Kiểm tra trực ban
      if (dutyMap[officerId]) {
        onDuty++;
        return;
      }

      // Kiểm tra có lịch làm việc không
      const schedules = scheduleMap[officerId];
      if (!schedules || schedules.length === 0) {
        notScheduled++;
        return;
      }

      // Kiểm tra đã điểm danh chưa
      const attendance = attendanceMap[officerId];
      if (attendance && attendance.length > 0) {
        working++;
      } else {
        absent++;
      }
    });

    return {
      working,
      onDuty,
      onLeave,
      absent,
      notScheduled
    };
  }

  /**
   * Tính toán thống kê cán bộ theo đơn vị cho officer summary
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @returns {Array} Thống kê theo đơn vị
   */
  async calculateOfficerSummaryByUnit(allOfficers, workSchedules, attendanceRecords, dutyShifts) {
    const currentTime = Date.now();
    const byUnit = StatisticsUtils.groupByUnit(allOfficers, 'units.0');

    const result = [];
    for (const [unitId, officers] of Object.entries(byUnit)) {
      if (unitId === 'unknown') continue;

      const unitName = officers[0]?.units?.[0]?.name || 'Không xác định';
      const total = officers.length;

      // Tính toán cho từng trạng thái
      let working = 0, onDuty = 0, onLeave = 0, absent = 0, notScheduled = 0;

      officers.forEach(officer => {
        const officerId = officer._id.toString();

        // Kiểm tra trực ban
        const isOnDuty = dutyShifts.some(shift =>
          shift.officer._id.toString() === officerId &&
          shift.startTime <= currentTime &&
          shift.endTime >= currentTime
        );

        if (isOnDuty) {
          onDuty++;
          return;
        }

        // Kiểm tra có lịch làm việc
        const hasSchedule = workSchedules.some(schedule =>
          schedule.user.toString() === officerId
        );

        if (!hasSchedule) {
          notScheduled++;
          return;
        }

        // Kiểm tra đã điểm danh
        const hasAttendance = attendanceRecords.some(record =>
          record.user._id.toString() === officerId
        );

        if (hasAttendance) {
          working++;
        } else {
          absent++;
        }
      });

      result.push({
        unitId,
        unitName,
        total,
        working,
        onDuty,
        onLeave,
        absent,
        notScheduled
      });
    }

    return result;
  }

  /**
   * Tính toán tỷ lệ điểm danh
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} byUnit - Thống kê theo đơn vị
   * @returns {Object} Tỷ lệ điểm danh
   */
  calculateAttendanceRate(workSchedules, attendanceRecords, byUnit) {
    const totalScheduled = _.sumBy(workSchedules, schedule => schedule.shifts.length);
    const totalAttended = attendanceRecords.length;

    const overall = StatisticsUtils.calculateRate(totalAttended, totalScheduled);

    const byUnitRates = byUnit.map(unit => {
      const unitScheduled = unit.working + unit.absent; // Tổng có lịch
      const unitAttended = unit.working; // Đã điểm danh

      return {
        unitId: unit.unitId,
        unitName: unit.unitName,
        rate: StatisticsUtils.calculateRate(unitAttended, unitScheduled)
      };
    });

    return {
      overall,
      byUnit: byUnitRates
    };
  }

  /**
   * Tính toán thống kê cán bộ theo khu vực
   * @param {Array} areas - Danh sách khu vực
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @returns {Array} Thống kê theo khu vực
   */
  async calculateOfficersByArea(areas, allOfficers, workSchedules, attendanceRecords, dutyShifts) {
    const currentTime = Date.now();

    const result = [];

    for (const area of areas) {
      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === area._id.toString())
      );

      const totalOfficers = areaOfficers.length;
      let workingOfficers = 0, onDutyOfficers = 0;

      // Tính toán trạng thái cho từng cán bộ
      const officers = [];
      const byUnit = {};

      areaOfficers.forEach(officer => {
        const officerId = officer._id.toString();

        // Kiểm tra trực ban
        const isOnDuty = dutyShifts.some(shift =>
          shift.officer._id.toString() === officerId &&
          shift.startTime <= currentTime &&
          shift.endTime >= currentTime
        );

        let status = 'absent';
        let currentActivity = 'Không có hoạt động';

        if (isOnDuty) {
          status = 'onDuty';
          currentActivity = 'Đang trực ban';
          onDutyOfficers++;
        } else {
          // Kiểm tra điểm danh
          const hasAttendance = attendanceRecords.some(record =>
            record.user._id.toString() === officerId
          );

          if (hasAttendance) {
            status = 'working';
            currentActivity = 'Đã điểm danh';
            workingOfficers++;
          }
        }

        // Thêm vào danh sách cán bộ
        officers.push({
          userId: officer._id,
          name: officer.name,
          unit: officer.units?.[0]?.name || 'Không xác định',
          status,
          currentActivity
        });

        // Thống kê theo đơn vị
        const unitName = officer.units?.[0]?.name || 'Không xác định';
        const unitId = officer.units?.[0]?._id || 'unknown';

        if (!byUnit[unitId]) {
          byUnit[unitId] = {
            unitId,
            unitName,
            officers: 0,
            working: 0,
            onDuty: 0
          };
        }

        byUnit[unitId].officers++;
        if (status === 'working') byUnit[unitId].working++;
        if (status === 'onDuty') byUnit[unitId].onDuty++;
      });

      // Tính tỷ lệ điểm danh
      const attendanceRate = StatisticsUtils.calculateRate(workingOfficers, totalOfficers);

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalOfficers,
          workingOfficers,
          onDutyOfficers,
          attendanceRate
        },
        byUnit: Object.values(byUnit),
        officers
      });
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho khu vực
   * @param {Array} areaStats - Thống kê theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalOfficers = _.sumBy(areaStats, 'summary.totalOfficers');
    const averageOfficersPerArea = totalAreas > 0 ? Math.round(totalOfficers / totalAreas) : 0;

    // Tìm khu vực có mật độ cán bộ cao nhất
    const highestDensityArea = _.maxBy(areaStats, 'summary.totalOfficers');

    return {
      totalAreas,
      totalOfficers,
      averageOfficersPerArea,
      highestDensityArea: highestDensityArea ? {
        areaId: highestDensityArea.areaId,
        areaName: highestDensityArea.areaName,
        officerCount: highestDensityArea.summary.totalOfficers
      } : null
    };
  }

  /**
   * Tính toán thống kê báo cáo theo khu vực
   * @param {Array} areas - Danh sách khu vực
   * @param {Array} reports - Danh sách báo cáo
   * @param {Array} allOfficers - Danh sách cán bộ
   * @returns {Array} Thống kê báo cáo theo khu vực
   */
  async calculateReportsByArea(areas, reports, allOfficers) {
    const result = [];

    for (const area of areas) {
      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === area._id.toString())
      );

      const areaOfficerIds = areaOfficers.map(officer => officer._id.toString());

      // Lấy báo cáo của khu vực này
      const areaReports = reports.filter(report => {
        // Ưu tiên lấy từ details.location.area nếu có
        if (report.details && report.details.length > 0) {
          return report.details.some(detail =>
            detail.location && detail.location.area &&
            detail.location.area._id.toString() === area._id.toString()
          );
        }

        // Nếu không có location.area, lấy từ createdBy.areas
        return report.createdBy && areaOfficerIds.includes(report.createdBy._id.toString());
      });

      // Tính tổng số vụ việc từ metrics thay vì đếm số báo cáo
      const totalIncidents = this.calculateTotalIncidentsFromReports(areaReports);

      // Thống kê theo loại báo cáo (vẫn đếm số báo cáo)
      const byReportType = _.countBy(areaReports, 'reportType');
      const byStatus = _.countBy(areaReports, 'status');
      const byWorkStatus = _.countBy(areaReports, 'workStatus');

      // Thống kê theo JobType (đếm số vụ việc từ metrics)
      const byJobType = this.calculateIncidentsByJobType(areaReports);

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalIncidents: totalIncidents, // Tổng số vụ việc từ metrics
          totalReports: areaReports.length, // Tổng số báo cáo
          totalOfficers: areaOfficers.length,
          incidentsPerOfficer: StatisticsUtils.calculateRate(totalIncidents, areaOfficers.length),
          reportsPerOfficer: StatisticsUtils.calculateRate(areaReports.length, areaOfficers.length)
        },
        byReportType: {
          quick: byReportType.quick || 0,
          detail: byReportType.detail || 0
        },
        byStatus: {
          submitted: byStatus.submitted || 0,
          approved: byStatus.approved || 0,
          rejected: byStatus.rejected || 0
        },
        byWorkStatus: {
          pending: byWorkStatus.pending || 0,
          in_progress: byWorkStatus.in_progress || 0,
          completed: byWorkStatus.completed || 0,
          cancelled: byWorkStatus.cancelled || 0,
          on_hold: byWorkStatus.on_hold || 0
        },
        byJobType,
        reports: areaReports.map(report => ({
          id: report._id,
          title: report.title,
          reportType: report.reportType,
          status: report.status,
          workStatus: report.workStatus,
          jobType: report.jobType?.name,
          createdBy: report.createdBy?.name,
          createdAt: report.createdAt,
          incidents: this.calculateTotalIncidentsFromReports([report]) // Số vụ việc trong báo cáo này
        }))
      });
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho báo cáo theo khu vực
   * @param {Array} areaStats - Thống kê báo cáo theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateReportAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalIncidents = areaStats.reduce((sum, area) => sum + area.summary.totalIncidents, 0);
    const totalReports = areaStats.reduce((sum, area) => sum + area.summary.totalReports, 0);
    const totalOfficers = areaStats.reduce((sum, area) => sum + area.summary.totalOfficers, 0);

    const averageIncidentsPerArea = StatisticsUtils.calculateRate(totalIncidents, totalAreas);
    const averageIncidentsPerOfficer = StatisticsUtils.calculateRate(totalIncidents, totalOfficers);
    const averageReportsPerArea = StatisticsUtils.calculateRate(totalReports, totalAreas);
    const averageReportsPerOfficer = StatisticsUtils.calculateRate(totalReports, totalOfficers);

    // Tìm khu vực có nhiều vụ việc nhất
    const maxIncidentsArea = areaStats.reduce((max, area) =>
      area.summary.totalIncidents > max.totalIncidents ?
        { areaName: area.areaName, totalIncidents: area.summary.totalIncidents } : max,
      { areaName: '', totalIncidents: 0 }
    );

    return {
      totalAreas,
      totalIncidents, // Tổng số vụ việc (từ metrics)
      totalReports,   // Tổng số báo cáo
      totalOfficers,
      averageIncidentsPerArea,
      averageIncidentsPerOfficer,
      averageReportsPerArea,
      averageReportsPerOfficer,
      maxIncidentsArea
    };
  }

  /**
   * Tính toán thống kê tổng quan báo cáo
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê tổng quan
   */
  calculateReportsSummary(reports) {
    const totalReports = reports.length;
    const totalIncidents = this.calculateTotalIncidentsFromReports(reports);

    const quickReports = reports.filter(r => r.reportType === 'quick').length;
    const detailReports = reports.filter(r => r.reportType === 'detail').length;

    // Tính số vụ việc theo loại báo cáo
    const quickIncidents = this.calculateTotalIncidentsFromReports(
      reports.filter(r => r.reportType === 'quick')
    );
    const detailIncidents = this.calculateTotalIncidentsFromReports(
      reports.filter(r => r.reportType === 'detail')
    );

    const submittedReports = reports.filter(r => r.status === 'submitted').length;
    const approvedReports = reports.filter(r => r.status === 'approved').length;
    const rejectedReports = reports.filter(r => r.status === 'rejected').length;

    const completedReports = reports.filter(r => r.workStatus === 'completed').length;
    const inProgressReports = reports.filter(r => r.workStatus === 'in_progress').length;
    const pendingReports = reports.filter(r => r.workStatus === 'pending').length;

    return {
      totalReports,
      totalIncidents, // Tổng số vụ việc từ metrics
      quickReports,
      detailReports,
      quickIncidents, // Số vụ việc từ báo cáo nhanh
      detailIncidents, // Số vụ việc từ báo cáo chi tiết
      submittedReports,
      approvedReports,
      rejectedReports,
      completedReports,
      inProgressReports,
      pendingReports,
      quickReportRate: StatisticsUtils.calculateRate(quickReports, totalReports),
      quickIncidentRate: StatisticsUtils.calculateRate(quickIncidents, totalIncidents),
      approvalRate: StatisticsUtils.calculateRate(approvedReports, totalReports),
      completionRate: StatisticsUtils.calculateRate(completedReports, totalReports),
      incidentsPerReport: StatisticsUtils.calculateRate(totalIncidents, totalReports)
    };
  }

  /**
   * Tính toán thống kê báo cáo theo trạng thái
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê theo trạng thái
   */
  calculateReportsByStatus(reports) {
    const byStatus = _.countBy(reports, 'status');
    const byWorkStatus = _.countBy(reports, 'workStatus');

    return {
      status: {
        draft: byStatus.draft || 0,
        submitted: byStatus.submitted || 0,
        approved: byStatus.approved || 0,
        rejected: byStatus.rejected || 0
      },
      workStatus: {
        pending: byWorkStatus.pending || 0,
        in_progress: byWorkStatus.in_progress || 0,
        completed: byWorkStatus.completed || 0,
        cancelled: byWorkStatus.cancelled || 0,
        on_hold: byWorkStatus.on_hold || 0
      }
    };
  }

  /**
   * Tính toán thống kê báo cáo theo loại
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê theo loại báo cáo
   */
  calculateReportsByType(reports) {
    const byType = _.countBy(reports, 'reportType');
    const total = reports.length;

    return {
      quick: {
        count: byType.quick || 0,
        percentage: StatisticsUtils.calculateRate(byType.quick || 0, total)
      },
      detail: {
        count: byType.detail || 0,
        percentage: StatisticsUtils.calculateRate(byType.detail || 0, total)
      }
    };
  }

  /**
   * Tính toán thống kê báo cáo theo loại công việc
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Array} Thống kê theo JobType
   */
  calculateReportsByJobType(reports) {
    const byJobType = {};
    const total = reports.length;

    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeName = report.jobType.name || 'Không xác định';
        const jobTypeId = report.jobType._id || 'unknown';

        if (!byJobType[jobTypeId]) {
          byJobType[jobTypeId] = {
            jobTypeId,
            jobTypeName,
            count: 0
          };
        }
        byJobType[jobTypeId].count++;
      }
    });

    return Object.values(byJobType).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculateRate(item.count, total)
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Tính toán thống kê báo cáo theo đơn vị
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Array} Thống kê theo đơn vị
   */
  async calculateReportsByUnit(reports) {
    const byUnit = {};
    const total = reports.length;

    reports.forEach(report => {
      if (report.unit) {
        const unitName = report.unit.name || 'Không xác định';
        const unitId = report.unit._id || 'unknown';

        if (!byUnit[unitId]) {
          byUnit[unitId] = {
            unitId,
            unitName,
            count: 0
          };
        }
        byUnit[unitId].count++;
      } else if (report.createdBy && report.createdBy.units && report.createdBy.units.length > 0) {
        // Fallback: lấy từ đơn vị của người tạo
        const unit = report.createdBy.units[0];
        const unitName = unit.name || 'Không xác định';
        const unitId = unit._id || 'unknown';

        if (!byUnit[unitId]) {
          byUnit[unitId] = {
            unitId,
            unitName,
            count: 0
          };
        }
        byUnit[unitId].count++;
      }
    });

    return Object.values(byUnit).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculateRate(item.count, total)
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Tính toán thống kê báo cáo theo thời gian
   * @param {Array} reports - Danh sách báo cáo
   * @param {String} groupBy - Nhóm theo: 'hour', 'day', 'week', 'month'
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Thống kê theo thời gian
   */
  calculateReportsTimeline(reports, groupBy, period) {
    const moment = require('moment');
    const timeline = [];

    // Tạo các time slots dựa trên groupBy
    const startMoment = moment(period.startTimestamp);
    const endMoment = moment(period.endTimestamp);

    let current = startMoment.clone();
    const format = this.getTimelineFormat(groupBy);
    const increment = this.getTimelineIncrement(groupBy);

    while (current.isSameOrBefore(endMoment)) {
      const timeSlot = current.format(format);
      const slotStart = current.clone().startOf(increment).valueOf();
      const slotEnd = current.clone().endOf(increment).valueOf();

      // Lọc báo cáo trong time slot này
      const slotReports = reports.filter(report =>
        report.createdAt >= slotStart && report.createdAt <= slotEnd
      );

      // Thống kê cho time slot
      const totalIncidents = this.calculateTotalIncidentsFromReports(slotReports);
      const quickReports = slotReports.filter(r => r.reportType === 'quick').length;
      const detailReports = slotReports.filter(r => r.reportType === 'detail').length;
      const quickIncidents = this.calculateTotalIncidentsFromReports(
        slotReports.filter(r => r.reportType === 'quick')
      );
      const detailIncidents = this.calculateTotalIncidentsFromReports(
        slotReports.filter(r => r.reportType === 'detail')
      );
      const submittedReports = slotReports.filter(r => r.status === 'submitted').length;
      const approvedReports = slotReports.filter(r => r.status === 'approved').length;

      timeline.push({
        timeSlot,
        timestamp: slotStart,
        totalReports: slotReports.length,
        totalIncidents, // Tổng số vụ việc trong time slot
        quickReports,
        detailReports,
        quickIncidents, // Số vụ việc từ báo cáo nhanh
        detailIncidents, // Số vụ việc từ báo cáo chi tiết
        submittedReports,
        approvedReports,
        reports: slotReports.map(r => ({
          id: r._id,
          title: r.title,
          reportType: r.reportType,
          status: r.status,
          createdAt: r.createdAt,
          incidents: this.calculateTotalIncidentsFromReports([r])
        }))
      });

      current.add(1, increment);
    }

    return timeline;
  }

  /**
   * Lấy format cho timeline dựa trên groupBy
   * @param {String} groupBy - Nhóm theo
   * @returns {String} Format string
   */
  getTimelineFormat(groupBy) {
    switch (groupBy) {
      case 'hour': return 'DD/MM/YYYY HH:00';
      case 'day': return 'DD/MM/YYYY';
      case 'week': return 'DD/MM/YYYY';
      case 'month': return 'MM/YYYY';
      default: return 'DD/MM/YYYY';
    }
  }

  /**
   * Lấy increment cho timeline dựa trên groupBy
   * @param {String} groupBy - Nhóm theo
   * @returns {String} Increment string
   */
  getTimelineIncrement(groupBy) {
    switch (groupBy) {
      case 'hour': return 'hour';
      case 'day': return 'day';
      case 'week': return 'week';
      case 'month': return 'month';
      default: return 'day';
    }
  }

  /**
   * Tính toán thống kê tổng quan cho timeline
   * @param {Array} timeline - Dữ liệu timeline
   * @returns {Object} Thống kê tổng quan
   */
  calculateTimelineSummary(timeline) {
    const totalSlots = timeline.length;
    const totalReports = timeline.reduce((sum, slot) => sum + slot.totalReports, 0);
    const totalIncidents = timeline.reduce((sum, slot) => sum + slot.totalIncidents, 0);
    const averageReportsPerSlot = StatisticsUtils.calculateRate(totalReports, totalSlots);
    const averageIncidentsPerSlot = StatisticsUtils.calculateRate(totalIncidents, totalSlots);

    // Tìm time slot có nhiều vụ việc nhất
    const peakSlot = timeline.reduce((max, slot) =>
      slot.totalIncidents > max.totalIncidents ? slot : max,
      { timeSlot: '', totalIncidents: 0, totalReports: 0 }
    );

    // Tính trend dựa trên số vụ việc (so sánh nửa đầu và nửa sau)
    const midPoint = Math.floor(totalSlots / 2);
    const firstHalf = timeline.slice(0, midPoint);
    const secondHalf = timeline.slice(midPoint);

    const firstHalfAvg = firstHalf.length > 0 ?
      firstHalf.reduce((sum, slot) => sum + slot.totalIncidents, 0) / firstHalf.length : 0;
    const secondHalfAvg = secondHalf.length > 0 ?
      secondHalf.reduce((sum, slot) => sum + slot.totalIncidents, 0) / secondHalf.length : 0;

    const trend = secondHalfAvg > firstHalfAvg ? 'increasing' :
                  secondHalfAvg < firstHalfAvg ? 'decreasing' : 'stable';

    return {
      totalSlots,
      totalReports,
      totalIncidents, // Tổng số vụ việc
      averageReportsPerSlot,
      averageIncidentsPerSlot, // Trung bình số vụ việc mỗi slot
      peakSlot: {
        timeSlot: peakSlot.timeSlot,
        totalReports: peakSlot.totalReports,
        totalIncidents: peakSlot.totalIncidents // Số vụ việc tại peak
      },
      trend,
      trendPercentage: firstHalfAvg > 0 ?
        Math.round(((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100) : 0
    };
  }

  /**
   * Tính tổng số vụ việc từ metrics của các báo cáo
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Number} Tổng số vụ việc
   */
  calculateTotalIncidentsFromReports(reports) {
    let totalIncidents = 0;

    reports.forEach(report => {
      if (report.metrics && typeof report.metrics === 'object') {
        // Tính tổng tất cả các giá trị số trong metrics
        Object.values(report.metrics).forEach(value => {
          if (typeof value === 'number' && value > 0) {
            totalIncidents += value;
          }
        });
      }
    });

    return totalIncidents;
  }

  /**
   * Tính số vụ việc theo JobType từ metrics
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê số vụ việc theo JobType
   */
  calculateIncidentsByJobType(reports) {
    const byJobType = {};

    reports.forEach(report => {
      if (report.jobType) {
        const jobTypeName = report.jobType.name || 'Không xác định';
        const incidents = this.calculateTotalIncidentsFromReports([report]);

        if (!byJobType[jobTypeName]) {
          byJobType[jobTypeName] = 0;
        }
        byJobType[jobTypeName] += incidents;
      }
    });

    return byJobType;
  }

  /**
   * Tính toán thống kê trạng thái chi tiết
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê trạng thái chi tiết
   */
  calculateDetailedStatusStats(reports) {
    const total = reports.length;
    const byStatus = _.countBy(reports, 'status');
    const byWorkStatus = _.countBy(reports, 'workStatus');
    const byReportType = _.countBy(reports, 'reportType');

    // Thống kê theo status
    const statusBreakdown = {
      draft: {
        count: byStatus.draft || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.draft || 0, total)
      },
      submitted: {
        count: byStatus.submitted || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.submitted || 0, total)
      },
      approved: {
        count: byStatus.approved || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.approved || 0, total)
      },
      rejected: {
        count: byStatus.rejected || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.rejected || 0, total)
      }
    };

    // Thống kê theo workStatus
    const workStatusBreakdown = {
      pending: {
        count: byWorkStatus.pending || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.pending || 0, total)
      },
      in_progress: {
        count: byWorkStatus.in_progress || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.in_progress || 0, total)
      },
      completed: {
        count: byWorkStatus.completed || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.completed || 0, total)
      },
      cancelled: {
        count: byWorkStatus.cancelled || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.cancelled || 0, total)
      },
      on_hold: {
        count: byWorkStatus.on_hold || 0,
        percentage: StatisticsUtils.calculateRate(byWorkStatus.on_hold || 0, total)
      }
    };

    return {
      total,
      statusBreakdown,
      workStatusBreakdown,
      reportTypeBreakdown: {
        quick: {
          count: byReportType.quick || 0,
          percentage: StatisticsUtils.calculateRate(byReportType.quick || 0, total)
        },
        detail: {
          count: byReportType.detail || 0,
          percentage: StatisticsUtils.calculateRate(byReportType.detail || 0, total)
        }
      }
    };
  }

  /**
   * Tính toán thống kê workflow
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê workflow
   */
  calculateWorkflowStats(reports) {
    const total = reports.length;

    // Báo cáo đã hoàn thành workflow (approved hoặc rejected)
    const completedWorkflow = reports.filter(r =>
      r.status === 'approved' || r.status === 'rejected'
    ).length;

    // Báo cáo đang chờ xử lý
    const pendingWorkflow = reports.filter(r => r.status === 'submitted').length;

    // Báo cáo nháp
    const draftReports = reports.filter(r => r.status === 'draft').length;

    // Tỷ lệ approval
    const approvedReports = reports.filter(r => r.status === 'approved').length;
    const submittedReports = reports.filter(r => r.status !== 'draft').length;
    const approvalRate = StatisticsUtils.calculateRate(approvedReports, submittedReports);

    // Tỷ lệ completion (work status)
    const completedWork = reports.filter(r => r.workStatus === 'completed').length;
    const completionRate = StatisticsUtils.calculateRate(completedWork, total);

    return {
      total,
      completedWorkflow,
      pendingWorkflow,
      draftReports,
      approvalRate,
      completionRate,
      workflowEfficiency: StatisticsUtils.calculateRate(completedWorkflow, total)
    };
  }

  /**
   * Tính toán thống kê thời gian xử lý
   * @param {Array} reports - Danh sách báo cáo
   * @returns {Object} Thống kê thời gian xử lý
   */
  calculateProcessingTimeStats(reports) {
    const processedReports = reports.filter(r =>
      r.status === 'approved' || r.status === 'rejected'
    );

    if (processedReports.length === 0) {
      return {
        averageProcessingTime: 0,
        fastestProcessing: 0,
        slowestProcessing: 0,
        processedCount: 0
      };
    }

    // Tính thời gian xử lý (giả sử có updatedAt)
    const processingTimes = processedReports.map(report => {
      // Thời gian từ khi tạo đến khi được approve/reject
      return report.updatedAt - report.createdAt;
    }).filter(time => time > 0);

    if (processingTimes.length === 0) {
      return {
        averageProcessingTime: 0,
        fastestProcessing: 0,
        slowestProcessing: 0,
        processedCount: processedReports.length
      };
    }

    const averageProcessingTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;
    const fastestProcessing = Math.min(...processingTimes);
    const slowestProcessing = Math.max(...processingTimes);

    // Convert từ milliseconds sang hours
    return {
      averageProcessingTime: Math.round(averageProcessingTime / (1000 * 60 * 60) * 100) / 100,
      fastestProcessing: Math.round(fastestProcessing / (1000 * 60 * 60) * 100) / 100,
      slowestProcessing: Math.round(slowestProcessing / (1000 * 60 * 60) * 100) / 100,
      processedCount: processedReports.length
    };
  }

  /**
   * Lấy tất cả văn bản trong khoảng thời gian
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách văn bản
   */
  async getDocuments(period) {
    return await Document.find({
      createdAt: {
        $gte: period.startTimestamp,
        $lte: period.endTimestamp
      },
      deletedAt: { $exists: false }
    })
    .populate('createdBy', 'name idNumber units')
    .populate('assignedTo', 'name idNumber')
    .populate('unit', 'name')
    .populate('originalDocument', 'title documentNumber')
    .lean();
  }

  /**
   * Tính toán thống kê tổng quan văn bản
   * @param {Array} documents - Danh sách văn bản
   * @returns {Object} Thống kê tổng quan
   */
  calculateDocumentsSummary(documents) {
    const totalDocuments = documents.length;

    // Thống kê theo hướng
    const incomingDocs = documents.filter(d => d.direction === 'incoming').length;
    const outgoingDocs = documents.filter(d => d.direction === 'outgoing').length;
    const replyDocs = documents.filter(d => d.direction === 'reply').length;

    // Thống kê theo trạng thái
    const pendingDocs = documents.filter(d => d.status === 'pending').length;
    const processingDocs = documents.filter(d => d.status === 'processing').length;
    const completedDocs = documents.filter(d => d.status === 'completed').length;

    // Thống kê theo độ ưu tiên
    const urgentDocs = documents.filter(d => d.priority === 'urgent').length;
    const highPriorityDocs = documents.filter(d => d.priority === 'high').length;

    return {
      totalDocuments,
      incomingDocuments: incomingDocs,
      outgoingDocuments: outgoingDocs,
      replyDocuments: replyDocs,
      pendingDocuments: pendingDocs,
      processingDocuments: processingDocs,
      completedDocuments: completedDocs,
      urgentDocuments: urgentDocs,
      highPriorityDocuments: highPriorityDocs,
      incomingRate: StatisticsUtils.calculateRate(incomingDocs, totalDocuments),
      outgoingRate: StatisticsUtils.calculateRate(outgoingDocs, totalDocuments),
      replyRate: StatisticsUtils.calculateRate(replyDocs, totalDocuments),
      completionRate: StatisticsUtils.calculateRate(completedDocs, totalDocuments)
    };
  }

  /**
   * Tính toán thống kê văn bản theo hướng
   * @param {Array} documents - Danh sách văn bản
   * @returns {Object} Thống kê theo hướng
   */
  calculateDocumentsByDirection(documents) {
    const total = documents.length;
    const byDirection = _.countBy(documents, 'direction');

    return {
      incoming: {
        count: byDirection.incoming || 0,
        percentage: StatisticsUtils.calculateRate(byDirection.incoming || 0, total)
      },
      outgoing: {
        count: byDirection.outgoing || 0,
        percentage: StatisticsUtils.calculateRate(byDirection.outgoing || 0, total)
      },
      reply: {
        count: byDirection.reply || 0,
        percentage: StatisticsUtils.calculateRate(byDirection.reply || 0, total)
      }
    };
  }

  /**
   * Tính toán thống kê văn bản theo trạng thái
   * @param {Array} documents - Danh sách văn bản
   * @returns {Object} Thống kê theo trạng thái
   */
  calculateDocumentsByStatus(documents) {
    const total = documents.length;
    const byStatus = _.countBy(documents, 'status');

    return {
      draft: {
        count: byStatus.draft || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.draft || 0, total)
      },
      pending: {
        count: byStatus.pending || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.pending || 0, total)
      },
      processing: {
        count: byStatus.processing || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.processing || 0, total)
      },
      completed: {
        count: byStatus.completed || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.completed || 0, total)
      },
      cancelled: {
        count: byStatus.cancelled || 0,
        percentage: StatisticsUtils.calculateRate(byStatus.cancelled || 0, total)
      }
    };
  }

  /**
   * Tính toán thống kê văn bản theo loại
   * @param {Array} documents - Danh sách văn bản
   * @returns {Array} Thống kê theo loại văn bản
   */
  calculateDocumentsByType(documents) {
    const total = documents.length;
    const byType = _.countBy(documents, 'documentType');

    return Object.keys(byType).map(type => ({
      type,
      count: byType[type],
      percentage: StatisticsUtils.calculateRate(byType[type], total)
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Tính toán thống kê văn bản theo đơn vị
   * @param {Array} documents - Danh sách văn bản
   * @returns {Array} Thống kê theo đơn vị
   */
  calculateDocumentsByUnit(documents) {
    const total = documents.length;
    const byUnit = {};

    documents.forEach(doc => {
      if (doc.unit) {
        const unitName = doc.unit.name || 'Không xác định';
        const unitId = doc.unit._id || 'unknown';

        if (!byUnit[unitId]) {
          byUnit[unitId] = {
            unitId,
            unitName,
            count: 0
          };
        }
        byUnit[unitId].count++;
      } else if (doc.createdBy && doc.createdBy.units && doc.createdBy.units.length > 0) {
        // Fallback: lấy từ đơn vị của người tạo
        const unit = doc.createdBy.units[0];
        const unitName = unit.name || 'Không xác định';
        const unitId = unit._id || 'unknown';

        if (!byUnit[unitId]) {
          byUnit[unitId] = {
            unitId,
            unitName,
            count: 0
          };
        }
        byUnit[unitId].count++;
      }
    });

    return Object.values(byUnit).map(item => ({
      ...item,
      percentage: StatisticsUtils.calculateRate(item.count, total)
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Tính toán thống kê xử lý văn bản
   * @param {Array} documents - Danh sách văn bản
   * @returns {Object} Thống kê xử lý
   */
  calculateDocumentProcessingStats(documents) {
    const total = documents.length;
    const currentTime = Date.now();

    // Văn bản quá hạn
    const overdueDocs = documents.filter(doc => {
      if (!doc.deadline) return false;
      const deadlineTime = StatisticsUtils.convertDateStringToTimestamp(doc.deadline);
      return deadlineTime < currentTime && doc.status !== 'completed';
    }).length;

    // Văn bản sắp hết hạn (trong 3 ngày tới)
    const threeDaysFromNow = currentTime + (3 * 24 * 60 * 60 * 1000);
    const dueSoonDocs = documents.filter(doc => {
      if (!doc.deadline) return false;
      const deadlineTime = StatisticsUtils.convertDateStringToTimestamp(doc.deadline);
      return deadlineTime > currentTime && deadlineTime <= threeDaysFromNow && doc.status !== 'completed';
    }).length;

    // Thời gian xử lý trung bình
    const completedDocs = documents.filter(d => d.status === 'completed');
    let averageProcessingTime = 0;

    if (completedDocs.length > 0) {
      const totalProcessingTime = completedDocs.reduce((sum, doc) => {
        return sum + (doc.updatedAt - doc.createdAt);
      }, 0);
      averageProcessingTime = totalProcessingTime / completedDocs.length;
      // Convert từ milliseconds sang days
      averageProcessingTime = Math.round(averageProcessingTime / (1000 * 60 * 60 * 24) * 100) / 100;
    }

    return {
      totalDocuments: total,
      overdueDocs,
      dueSoonDocs,
      completedDocs: completedDocs.length,
      averageProcessingTime, // Tính bằng ngày
      overdueRate: StatisticsUtils.calculateRate(overdueDocs, total),
      dueSoonRate: StatisticsUtils.calculateRate(dueSoonDocs, total),
      onTimeCompletionRate: StatisticsUtils.calculateRate(
        completedDocs.filter(doc => {
          if (!doc.deadline) return true;
          const deadlineTime = StatisticsUtils.convertDateStringToTimestamp(doc.deadline);
          return doc.updatedAt <= deadlineTime;
        }).length,
        completedDocs.length
      )
    };
  }
}

module.exports = new StatisticsService();
