const _ = require('lodash');
const moment = require('moment');
const StatisticsUtils = require('../utils/statisticsUtils');

// Models
const User = require('../models/user');
const DutyShift = require('../models/dutyShift');
const AttendanceRecord = require('../models/attendanceRecord');
const WorkSchedule = require('../models/workSchedule');
const LeaveRequest = require('../models/leaveRequest');
const Area = require('../models/area');

// Duty Schedule Models
const DutyMainSchedule = require('../models/dutyMainSchedule');
const DutySubSchedule = require('../models/dutySubSchedule');
const DutyLocationSchedule = require('../models/dutyLocationSchedule');
const DutyPatrolSchedule = require('../models/dutyPatrolSchedule');
const DutyStadiumSchedule = require('../models/dutyStadiumSchedule');
const DutyCriminalSchedule = require('../models/dutyCriminalSchedule');
const DutySpecializedSchedule = require('../models/dutySpecializedSchedule');

/**
 * Service xử lý logic thống kê cho hệ thống
 * Cung cấp các phương thức tính toán thống kê cho 4 API chính
 */
class StatisticsService {

  /**
   * Lấy danh sách cán bộ đang trực ban
   * @param {Object} params - Tham số đầu vào
   * @param {String} params.timeRange - Khoảng thời gian: 'day', 'week', 'month', 'custom'
   * @param {String} params.startDate - Ngày bắt đầu (DD-MM-YYYY) cho custom range
   * @param {String} params.endDate - Ngày kết thúc (DD-MM-YYYY) cho custom range
   * @param {String} params.userId - ID người dùng (để check permission)
   * @returns {Object} Danh sách cán bộ trực ban và thống kê
   */
  async getOnDutyOfficers(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      // Tính toán khoảng thời gian
      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);
      const currentTime = Date.now();

      // Lấy tất cả ca trực trong khoảng thời gian
      const dutyShifts = await this.getAllDutyShifts(period);

      // Lọc ra các ca trực đang diễn ra
      const onDutyShifts = dutyShifts.filter(shift => {
        return shift.startTime <= currentTime && shift.endTime >= currentTime && shift.status === 1;
      });

      // Populate thông tin cán bộ
      const populatedShifts = await this.populateOfficerInfo(onDutyShifts);

      // Tính toán thống kê
      const summary = this.calculateOnDutySummary(populatedShifts);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách cán bộ trực ban thành công'
        },
        data: {
          period,
          currentTime,
          officers: populatedShifts,
          summary
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy danh sách cán bộ trực ban'
        }
      };
    }
  }

  /**
   * Thống kê số cán bộ đi muộn
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ đi muộn
   */
  async getLateAttendanceStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      // Validate tham số
      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu attendance và work schedule
      const [attendanceRecords, workSchedules] = await Promise.all([
        this.getAttendanceRecords(period),
        this.getWorkSchedules(period)
      ]);

      // Tính toán thống kê
      const summary = this.calculateLateAttendanceSummary(attendanceRecords, workSchedules);
      const byShift = this.calculateLateAttendanceByShift(attendanceRecords);
      const byUnit = await this.calculateLateAttendanceByUnit(attendanceRecords);
      const lateOfficers = await this.getLateOfficersDetail(attendanceRecords);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ đi muộn thành công'
        },
        data: {
          period,
          summary,
          byShift,
          byUnit,
          lateOfficers
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê cán bộ đi muộn'
        }
      };
    }
  }

  /**
   * Thống kê tổng số lượng cán bộ
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê tổng số lượng cán bộ
   */
  async getOfficerSummaryStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy tất cả dữ liệu cần thiết
      const [
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      ] = await Promise.all([
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period),
        this.getApprovedLeaveRequests(period)
      ]);

      // Tính toán thống kê tổng quan
      const summary = this.calculateOfficerSummary(allOfficers);
      const workStatus = this.calculateWorkStatus(
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts,
        leaveRequests
      );
      const byUnit = await this.calculateOfficerSummaryByUnit(
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      );
      const attendanceRate = this.calculateAttendanceRate(workSchedules, attendanceRecords, byUnit);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê tổng số lượng cán bộ thành công'
        },
        data: {
          period,
          summary,
          workStatus,
          byUnit,
          attendanceRate
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê tổng số lượng cán bộ'
        }
      };
    }
  }

  /**
   * Thống kê cán bộ theo khu vực
   * @param {Object} params - Tham số đầu vào
   * @returns {Object} Thống kê cán bộ theo khu vực
   */
  async getOfficersByAreaStats(params) {
    try {
      const { timeRange = 'day', startDate, endDate, userId } = params;

      const validation = StatisticsUtils.validateStatisticsParams({ timeRange, startDate, endDate });
      if (!validation.isValid) {
        return {
          success: false,
          message: {
            head: 'Lỗi tham số',
            body: validation.errors.join(', ')
          }
        };
      }

      const period = StatisticsUtils.getTimeRange(timeRange, startDate, endDate);

      // Lấy dữ liệu
      const [
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      ] = await Promise.all([
        this.getAllAreas(),
        this.getAllOfficers(),
        this.getWorkSchedules(period),
        this.getAttendanceRecords(period),
        this.getAllDutyShifts(period)
      ]);

      // Tính toán thống kê theo khu vực
      const areaStats = await this.calculateOfficersByArea(
        areas,
        allOfficers,
        workSchedules,
        attendanceRecords,
        dutyShifts
      );

      const summary = this.calculateAreaSummary(areaStats);

      return {
        success: true,
        message: {
          head: 'Thành công',
          body: 'Lấy thống kê cán bộ theo khu vực thành công'
        },
        data: {
          period,
          areas: areaStats,
          summary
        }
      };

    } catch (error) {
      return {
        success: false,
        message: {
          head: 'Lỗi hệ thống',
          body: 'Có lỗi xảy ra khi lấy thống kê cán bộ theo khu vực'
        }
      };
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /**
   * Lấy tất cả ca trực từ 8 loại lịch trực
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách tất cả ca trực
   */
  async getAllDutyShifts(period) {
    const dutyShifts = await DutyShift.find({
      startTime: { $gte: period.startTimestamp },
      endTime: { $lte: period.endTimestamp },
      status: { $ne: 2 } // Không lấy ca đã hủy
    })
    .populate('officer', 'name avatar idNumber units position areas')
    .populate('unit', 'name')
    .lean();

    return dutyShifts;
  }

  /**
   * Populate thông tin chi tiết cán bộ cho ca trực
   * @param {Array} dutyShifts - Danh sách ca trực
   * @returns {Array} Ca trực đã được populate
   */
  async populateOfficerInfo(dutyShifts) {
    return dutyShifts.map(shift => ({
      userId: shift.officer._id,
      name: shift.officer.name,
      avatar: shift.officer.avatar,
      idNumber: shift.officer.idNumber,
      unit: shift.unit,
      position: shift.officer.position,
      dutyInfo: {
        dutyType: this.getDutyType(shift),
        dutyName: shift.name,
        location: shift.locationDuty,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: shift.status,
        forLeader: shift.forLeader,
        hasEquipment: shift.hasEquipment
      }
    }));
  }

  /**
   * Xác định loại trực từ ca trực
   * @param {Object} shift - Thông tin ca trực
   * @returns {String} Loại trực
   */
  getDutyType(shift) {
    // Logic xác định loại trực dựa trên source hoặc các field khác
    if (shift.source) {
      return shift.source;
    }

    // Fallback logic
    if (shift.locationDuty) return 'location';
    if (shift.forLeader) return 'main';
    return 'general';
  }

  /**
   * Tính toán thống kê tổng quan cho cán bộ trực ban
   * @param {Array} onDutyShifts - Danh sách ca trực đang diễn ra
   * @returns {Object} Thống kê tổng quan
   */
  calculateOnDutySummary(onDutyShifts) {
    const totalOnDuty = onDutyShifts.length;

    const byDutyType = _.countBy(onDutyShifts, 'dutyInfo.dutyType');
    const byStatus = _.countBy(onDutyShifts, 'dutyInfo.status');

    return {
      totalOnDuty,
      byDutyType,
      byStatus: {
        confirmed: byStatus[1] || 0,
        pending: byStatus[0] || 0,
        cancelled: byStatus[2] || 0
      }
    };
  }

  /**
   * Lấy dữ liệu attendance records
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách attendance records
   */
  async getAttendanceRecords(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await AttendanceRecord.find(dateQuery)
      .populate('user', 'name idNumber units position')
      .lean();
  }

  /**
   * Lấy dữ liệu work schedules
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách work schedules
   */
  async getWorkSchedules(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('date', period, 'ddmmyyyy');

    return await WorkSchedule.find({
      ...dateQuery,
      status: 1
    })
    .populate('user', 'name idNumber units position')
    .lean();
  }

  /**
   * Lấy tất cả cán bộ
   * @returns {Array} Danh sách cán bộ
   */
  async getAllOfficers() {
    return await User.find({ status: 1 })
      .populate('units', 'name')
      .populate('position', 'name')
      .populate('areas', 'name level')
      .lean();
  }

  /**
   * Lấy tất cả khu vực
   * @returns {Array} Danh sách khu vực
   */
  async getAllAreas() {
    return await Area.find({ status: 1, level: 1 }) // Chỉ lấy level 1
      .lean();
  }

  /**
   * Lấy đơn xin nghỉ đã được duyệt
   * @param {Object} period - Khoảng thời gian
   * @returns {Array} Danh sách đơn xin nghỉ
   */
  async getApprovedLeaveRequests(period) {
    const dateQuery = StatisticsUtils.createTimeRangeQuery('startDate', period, 'ddmmyyyy');

    return await LeaveRequest.find({
      ...dateQuery,
      status: 'approved'
    })
    .populate('user', 'name idNumber')
    .lean();
  }

  /**
   * Tính toán thống kê cán bộ đi muộn tổng quan
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @param {Array} workSchedules - Danh sách work schedules
   * @returns {Object} Thống kê tổng quan
   */
  calculateLateAttendanceSummary(attendanceRecords, workSchedules) {
    // Tính tổng số ca được lên lịch
    const totalScheduled = _.sumBy(workSchedules, schedule => schedule.shifts.length);

    // Đếm số ca đã điểm danh
    const totalCheckedIn = attendanceRecords.length;

    // Đếm số ca đi muộn
    const totalLate = attendanceRecords.filter(record => record.status === 'late').length;

    // Đếm số ca đúng giờ
    const totalOnTime = attendanceRecords.filter(record => record.status === 'on_time').length;

    // Tính tỷ lệ
    const lateRate = StatisticsUtils.calculateRate(totalLate, totalScheduled);
    const onTimeRate = StatisticsUtils.calculateRate(totalOnTime, totalScheduled);
    const absentRate = StatisticsUtils.calculateRate(totalScheduled - totalCheckedIn, totalScheduled);

    return {
      totalScheduled,
      totalCheckedIn,
      totalLate,
      lateRate,
      onTimeRate,
      absentRate
    };
  }

  /**
   * Tính toán thống kê cán bộ đi muộn theo ca
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @returns {Object} Thống kê theo ca
   */
  calculateLateAttendanceByShift(attendanceRecords) {
    const byShift = _.groupBy(attendanceRecords, 'shift');

    const result = {};
    ['morning', 'afternoon'].forEach(shift => {
      const shiftRecords = byShift[shift] || [];
      const scheduled = shiftRecords.length; // Tạm tính, cần cải thiện
      const checkedIn = shiftRecords.length;
      const late = shiftRecords.filter(r => r.status === 'late').length;

      result[shift] = {
        scheduled,
        checkedIn,
        late,
        lateRate: StatisticsUtils.calculateRate(late, scheduled)
      };
    });

    return result;
  }

  /**
   * Tính toán thống kê cán bộ đi muộn theo đơn vị
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @returns {Array} Thống kê theo đơn vị
   */
  async calculateLateAttendanceByUnit(attendanceRecords) {
    const byUnit = StatisticsUtils.groupByUnit(attendanceRecords, 'user.units.0');

    const result = [];
    for (const [unitId, records] of Object.entries(byUnit)) {
      if (unitId === 'unknown') continue;

      const scheduled = records.length; // Tạm tính
      const late = records.filter(r => r.status === 'late').length;
      const unitName = records[0]?.user?.units?.[0]?.name || 'Không xác định';

      result.push({
        unitId,
        unitName,
        scheduled,
        late,
        lateRate: StatisticsUtils.calculateRate(late, scheduled)
      });
    }

    return result;
  }

  /**
   * Lấy chi tiết cán bộ đi muộn
   * @param {Array} attendanceRecords - Danh sách attendance records
   * @returns {Array} Chi tiết cán bộ đi muộn
   */
  async getLateOfficersDetail(attendanceRecords) {
    const lateRecords = attendanceRecords.filter(record => record.status === 'late');

    return lateRecords.map(record => {
      const scheduledTime = record.shift === 'morning' ? '08:00' : '14:00';
      const checkinTime = new Date(record.checkinTime);
      const scheduledDateTime = moment(record.date, 'DD-MM-YYYY')
        .set({
          hour: parseInt(scheduledTime.split(':')[0]),
          minute: parseInt(scheduledTime.split(':')[1])
        });

      const lateMinutes = Math.max(0, moment(checkinTime).diff(scheduledDateTime, 'minutes'));

      return {
        userId: record.user._id,
        name: record.user.name,
        idNumber: record.user.idNumber,
        unit: record.user.units?.[0]?.name || 'Không xác định',
        shift: record.shift,
        scheduledTime,
        checkinTime: record.checkinTime,
        lateMinutes
      };
    });
  }

  /**
   * Tính toán thống kê tổng quan cán bộ
   * @param {Array} allOfficers - Danh sách tất cả cán bộ
   * @returns {Object} Thống kê tổng quan
   */
  calculateOfficerSummary(allOfficers) {
    const totalOfficers = allOfficers.length;
    const activeOfficers = allOfficers.filter(officer => officer.status === 1).length;
    const inactiveOfficers = totalOfficers - activeOfficers;

    return {
      totalOfficers,
      activeOfficers,
      inactiveOfficers
    };
  }

  /**
   * Tính toán trạng thái làm việc của cán bộ
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @param {Array} leaveRequests - Đơn xin nghỉ
   * @returns {Object} Thống kê trạng thái làm việc
   */
  calculateWorkStatus(allOfficers, workSchedules, attendanceRecords, dutyShifts, leaveRequests) {
    const currentTime = Date.now();

    // Tạo map để tra cứu nhanh
    const attendanceMap = _.groupBy(attendanceRecords, 'user._id');
    const scheduleMap = _.groupBy(workSchedules, 'user');
    const dutyMap = _.groupBy(dutyShifts.filter(s =>
      s.startTime <= currentTime && s.endTime >= currentTime
    ), 'officer._id');
    const leaveMap = _.groupBy(leaveRequests, 'user._id');

    let working = 0, onDuty = 0, onLeave = 0, absent = 0, notScheduled = 0;

    allOfficers.forEach(officer => {
      const officerId = officer._id.toString();

      // Kiểm tra nghỉ phép
      if (leaveMap[officerId]) {
        onLeave++;
        return;
      }

      // Kiểm tra trực ban
      if (dutyMap[officerId]) {
        onDuty++;
        return;
      }

      // Kiểm tra có lịch làm việc không
      const schedules = scheduleMap[officerId];
      if (!schedules || schedules.length === 0) {
        notScheduled++;
        return;
      }

      // Kiểm tra đã điểm danh chưa
      const attendance = attendanceMap[officerId];
      if (attendance && attendance.length > 0) {
        working++;
      } else {
        absent++;
      }
    });

    return {
      working,
      onDuty,
      onLeave,
      absent,
      notScheduled
    };
  }

  /**
   * Tính toán thống kê cán bộ theo đơn vị cho officer summary
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @returns {Array} Thống kê theo đơn vị
   */
  async calculateOfficerSummaryByUnit(allOfficers, workSchedules, attendanceRecords, dutyShifts) {
    const currentTime = Date.now();
    const byUnit = StatisticsUtils.groupByUnit(allOfficers, 'units.0');

    const result = [];
    for (const [unitId, officers] of Object.entries(byUnit)) {
      if (unitId === 'unknown') continue;

      const unitName = officers[0]?.units?.[0]?.name || 'Không xác định';
      const total = officers.length;

      // Tính toán cho từng trạng thái
      let working = 0, onDuty = 0, onLeave = 0, absent = 0, notScheduled = 0;

      officers.forEach(officer => {
        const officerId = officer._id.toString();

        // Kiểm tra trực ban
        const isOnDuty = dutyShifts.some(shift =>
          shift.officer._id.toString() === officerId &&
          shift.startTime <= currentTime &&
          shift.endTime >= currentTime
        );

        if (isOnDuty) {
          onDuty++;
          return;
        }

        // Kiểm tra có lịch làm việc
        const hasSchedule = workSchedules.some(schedule =>
          schedule.user.toString() === officerId
        );

        if (!hasSchedule) {
          notScheduled++;
          return;
        }

        // Kiểm tra đã điểm danh
        const hasAttendance = attendanceRecords.some(record =>
          record.user._id.toString() === officerId
        );

        if (hasAttendance) {
          working++;
        } else {
          absent++;
        }
      });

      result.push({
        unitId,
        unitName,
        total,
        working,
        onDuty,
        onLeave,
        absent,
        notScheduled
      });
    }

    return result;
  }

  /**
   * Tính toán tỷ lệ điểm danh
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} byUnit - Thống kê theo đơn vị
   * @returns {Object} Tỷ lệ điểm danh
   */
  calculateAttendanceRate(workSchedules, attendanceRecords, byUnit) {
    const totalScheduled = _.sumBy(workSchedules, schedule => schedule.shifts.length);
    const totalAttended = attendanceRecords.length;

    const overall = StatisticsUtils.calculateRate(totalAttended, totalScheduled);

    const byUnitRates = byUnit.map(unit => {
      const unitScheduled = unit.working + unit.absent; // Tổng có lịch
      const unitAttended = unit.working; // Đã điểm danh

      return {
        unitId: unit.unitId,
        unitName: unit.unitName,
        rate: StatisticsUtils.calculateRate(unitAttended, unitScheduled)
      };
    });

    return {
      overall,
      byUnit: byUnitRates
    };
  }

  /**
   * Tính toán thống kê cán bộ theo khu vực
   * @param {Array} areas - Danh sách khu vực
   * @param {Array} allOfficers - Danh sách cán bộ
   * @param {Array} workSchedules - Lịch làm việc
   * @param {Array} attendanceRecords - Bản ghi điểm danh
   * @param {Array} dutyShifts - Ca trực
   * @returns {Array} Thống kê theo khu vực
   */
  async calculateOfficersByArea(areas, allOfficers, workSchedules, attendanceRecords, dutyShifts) {
    const currentTime = Date.now();

    const result = [];

    for (const area of areas) {
      // Lấy cán bộ thuộc khu vực này
      const areaOfficers = allOfficers.filter(officer =>
        officer.areas && officer.areas.some(a => a._id.toString() === area._id.toString())
      );

      const totalOfficers = areaOfficers.length;
      let workingOfficers = 0, onDutyOfficers = 0;

      // Tính toán trạng thái cho từng cán bộ
      const officers = [];
      const byUnit = {};

      areaOfficers.forEach(officer => {
        const officerId = officer._id.toString();

        // Kiểm tra trực ban
        const isOnDuty = dutyShifts.some(shift =>
          shift.officer._id.toString() === officerId &&
          shift.startTime <= currentTime &&
          shift.endTime >= currentTime
        );

        let status = 'absent';
        let currentActivity = 'Không có hoạt động';

        if (isOnDuty) {
          status = 'onDuty';
          currentActivity = 'Đang trực ban';
          onDutyOfficers++;
        } else {
          // Kiểm tra điểm danh
          const hasAttendance = attendanceRecords.some(record =>
            record.user._id.toString() === officerId
          );

          if (hasAttendance) {
            status = 'working';
            currentActivity = 'Đã điểm danh';
            workingOfficers++;
          }
        }

        // Thêm vào danh sách cán bộ
        officers.push({
          userId: officer._id,
          name: officer.name,
          unit: officer.units?.[0]?.name || 'Không xác định',
          status,
          currentActivity
        });

        // Thống kê theo đơn vị
        const unitName = officer.units?.[0]?.name || 'Không xác định';
        const unitId = officer.units?.[0]?._id || 'unknown';

        if (!byUnit[unitId]) {
          byUnit[unitId] = {
            unitId,
            unitName,
            officers: 0,
            working: 0,
            onDuty: 0
          };
        }

        byUnit[unitId].officers++;
        if (status === 'working') byUnit[unitId].working++;
        if (status === 'onDuty') byUnit[unitId].onDuty++;
      });

      // Tính tỷ lệ điểm danh
      const attendanceRate = StatisticsUtils.calculateRate(workingOfficers, totalOfficers);

      result.push({
        areaId: area._id,
        areaName: area.name,
        level: area.level,
        summary: {
          totalOfficers,
          workingOfficers,
          onDutyOfficers,
          attendanceRate
        },
        byUnit: Object.values(byUnit),
        officers
      });
    }

    return result;
  }

  /**
   * Tính toán thống kê tổng quan cho khu vực
   * @param {Array} areaStats - Thống kê theo khu vực
   * @returns {Object} Thống kê tổng quan
   */
  calculateAreaSummary(areaStats) {
    const totalAreas = areaStats.length;
    const totalOfficers = _.sumBy(areaStats, 'summary.totalOfficers');
    const averageOfficersPerArea = totalAreas > 0 ? Math.round(totalOfficers / totalAreas) : 0;

    // Tìm khu vực có mật độ cán bộ cao nhất
    const highestDensityArea = _.maxBy(areaStats, 'summary.totalOfficers');

    return {
      totalAreas,
      totalOfficers,
      averageOfficersPerArea,
      highestDensityArea: highestDensityArea ? {
        areaId: highestDensityArea.areaId,
        areaName: highestDensityArea.areaName,
        officerCount: highestDensityArea.summary.totalOfficers
      } : null
    };
  }
}

module.exports = new StatisticsService();
