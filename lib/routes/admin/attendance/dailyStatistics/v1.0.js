const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const attendanceService = require('../../../../services/attendanceService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API thống kê điểm danh theo ngày
 * POST /api/v1.0/admin/attendance/daily-statistics
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    unitId,
    startDate,
    endDate
  } = req.body;

  const validateParams = (next) => {
    const schema = Joi.object({
      unitId: Joi.objectId().optional(),
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).required(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).required()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate
    if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.ATTENDANCE.WRONG_DATE
      });
    }

    // Kiểm tra khoảng thời gian không quá 31 ngày
    const dateRange = DateUtils.generateDateRangeDDMMYYYY(startDate, endDate);
    if (dateRange.length > 31) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Khoảng thời gian không hợp lệ',
          body: 'Khoảng thời gian thống kê không được vượt quá 31 ngày'
        }
      });
    }

    next();
  };

  const getDailyStatistics = (next) => {
    try {
      attendanceService.getAttendanceStatisticsByDay(
        viewerId,
        unitId,
        startDate,
        endDate
      )
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            // message: res.message,
            data: res.data
          });
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  async.waterfall([
    validateParams,
    getDailyStatistics
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
