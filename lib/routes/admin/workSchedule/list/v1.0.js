const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const scheduleService = require('../../../../services/scheduleService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API lấy danh sách lịch làm việc được nhóm theo user với phân trang theo users
 * POST /api/v1.0/work-schedule/list
 *
 * Trả về dữ liệu được tổ chức theo từng user với:
 * - Thông tin user (name, idNumber, units)
 * - <PERSON><PERSON> sách lịch làm việc của user đó được sắp xếp theo ngày
 * - <PERSON>ân trang theo số lượng users (không phải schedules)
 * - Thống kê tổng quan (tổng số schedules, users, trung bình schedules/user)
 * - Hỗ trợ sắp xếp schedules theo ngày (cũ->mới hoặc mới->cũ)
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    startDate,
    endDate,
    userId,
    unitId,
    page = 1,
    limit = 20,
    sortOrder = 'desc' // 'asc': cũ->mới, 'desc': mới->cũ (default)
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      userId: Joi.objectId().optional(),
      unitId: Joi.objectId().optional(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc').optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai (định dạng DD-MM-YYYY)
    if (startDate && endDate) {
      if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.ATTENDANCE.WRONG_DATE
        });
      }
    }

    next();
  };

  const getScheduleList = (next) => {
    try {
      const filters = {
        startDate,
        endDate,
        userId,
        unitId,
        page,
        limit
      };

      // Sử dụng method mới để phân trang theo users
      scheduleService.getScheduleListByUsers(viewerId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Nhóm dữ liệu lịch làm việc theo user và sắp xếp
   * @param {Array} schedules - Danh sách lịch làm việc
   * @param {String} sortOrder - Thứ tự sắp xếp schedules: 'asc' (cũ->mới) hoặc 'desc' (mới->cũ)
   * @returns {Array} Dữ liệu được nhóm theo user
   */
  const groupSchedulesByUser = (schedules, sortOrder = 'desc') => {
    // Nhóm schedules theo user._id
    const groupedByUser = _.groupBy(schedules, (schedule) => {
      return schedule.user._id.toString();
    });

    // Chuyển đổi thành array với thông tin user và danh sách schedules
    const userSchedules = Object.keys(groupedByUser).map(userId => {
      const userScheduleList = groupedByUser[userId];
      const userInfo = userScheduleList[0].user; // Lấy thông tin user từ schedule đầu tiên

      // Sắp xếp schedules của user theo ngày
      const sortedSchedules = userScheduleList
        .map(schedule => ({
          _id: schedule._id,
          date: schedule.date,
          shifts: schedule.shifts,
          createdBy: schedule.createdBy,
          createdAt: schedule.createdAt,
          updatedAt: schedule.updatedAt,
          status: schedule.status
        }))
        .sort((a, b) => {
          // Convert DD-MM-YYYY to YYYY-MM-DD để so sánh
          const dateA = DateUtils.convertDDMMYYYYtoYYYYMMDD(a.date);
          const dateB = DateUtils.convertDDMMYYYYtoYYYYMMDD(b.date);

          if (sortOrder === 'asc') {
            return dateA.localeCompare(dateB); // Cũ đến mới
          } else {
            return dateB.localeCompare(dateA); // Mới đến cũ (default)
          }
        });

      return {
        user: {
          _id: userInfo._id,
          name: userInfo.name,
          idNumber: userInfo.idNumber,
          units: userInfo.units
        },
        schedules: sortedSchedules,
        totalSchedules: sortedSchedules.length
      };
    });

    // Sắp xếp users theo tên alphabetical (tiếng Việt)
    return userSchedules.sort((a, b) => {
      return a.user.name.localeCompare(b.user.name, 'vi', { sensitivity: 'base' });
    });
  };

  const formatResponse = (next) => {
    try {
      const { schedules, pagination } = result.data;

      // Nhóm dữ liệu theo user với sắp xếp schedules theo ngày
      const groupedData = groupSchedulesByUser(schedules, sortOrder);

      // Pagination đã được xử lý ở tầng service theo users
      const currentPageUsers = groupedData.length; // Số users trong trang hiện tại
      const totalUsers = pagination.totalUsers; // Tổng số users có schedules
      const totalSchedules = pagination.total; // Tổng số schedules

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thành công',
          body: 'Lấy danh sách lịch làm việc theo user thành công'
        },
        data: {
          userSchedules: groupedData,
          pagination: {
            page: pagination.page,
            limit: pagination.limit, // Limit áp dụng cho users
            total: totalSchedules, // Tổng số schedules (tất cả users)
            totalUsers: totalUsers, // Tổng số users có schedules
            pages: pagination.pages, // Tổng số trang (dựa trên users)
            currentPageUsers: currentPageUsers, // Số users trong trang hiện tại
            usersInCurrentPage: pagination.usersInCurrentPage || currentPageUsers
          },
          summary: {
            totalSchedules: totalSchedules,
            totalUsers: totalUsers,
            averageSchedulesPerUser: totalUsers > 0 ? Math.round(totalSchedules / totalUsers * 100) / 100 : 0,
            currentPageSchedules: groupedData.reduce((sum, user) => sum + user.totalSchedules, 0)
          }
        }
      });
    } catch (error) {
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  async.waterfall([
    validateParams,
    getScheduleList,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};