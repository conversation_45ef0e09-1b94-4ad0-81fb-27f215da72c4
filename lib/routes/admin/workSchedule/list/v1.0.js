const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const scheduleService = require('../../../../services/scheduleService');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const DateUtils = require('../../../../utils/dateUtils');

/**
 * API lấy danh sách lịch làm việc
 * POST /api/v1.0/work-schedule/list
 */
module.exports = (req, res) => {
  const viewerId = req.user.id;
  const {
    startDate,
    endDate,
    userId,
    unitId,
    page = 1,
    limit = 20
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Chỉ chấp nhận định dạng DD-MM-YYYY
      startDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      endDate: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional(),
      userId: Joi.objectId().optional(),
      unitId: Joi.objectId().optional(),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20)
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    // Kiểm tra startDate <= endDate nếu có cả hai (định dạng DD-MM-YYYY)
    if (startDate && endDate) {
      if (DateUtils.compareDDMMYYYY(startDate, endDate) > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.ATTENDANCE.WRONG_DATE
        });
      }
    }

    next();
  };

  const getScheduleList = (next) => {
    try {
      const filters = {
        startDate,
        endDate,
        userId,
        unitId,
        page,
        limit
      };

      scheduleService.getScheduleList(viewerId, filters)
        .then((res) => {
          if (!res || !res.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: res.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = res;

          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next({
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
    }
  };

  const formatResponse = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      // message: result.message,
      data: result.data
    });
  };

  async.waterfall([
    validateParams,
    getScheduleList,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};