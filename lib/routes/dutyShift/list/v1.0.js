const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { weekType } = req.body;
  const currentDate = new Date();
  let targetDate;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    
    // Validate weekType parameter nếu có
    if (weekType) {
      const validWeekTypes = ['previous', 'current', 'next'];
      if (!validWeekTypes.includes(weekType)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'weekType phải là previous, current hoặc next'
          }
        });
      }
    }
    
    
    next();
  };

  const getDutyShifts = (next) => {
    let query = {};
    let dateRange = {};

    // Nếu có weekType, tính toán khoảng thời gian tuần
    if (weekType) {
      targetDate = new Date(currentDate);
      
      if (weekType === 'previous') {
        targetDate.setDate(currentDate.getDate() - 7);
      } else if (weekType === 'next') {
        targetDate.setDate(currentDate.getDate() + 7);
      }

      // Tính toán startTime và endTime của tuần
      const startOfWeek = (() => {
        const date = new Date(targetDate);
        const day = date.getDay();
        const diff = date.getDate() - day + (day === 0 ? -6 : 1);
        const mondayDate = new Date(date.setDate(diff));
        mondayDate.setHours(0, 0, 0, 0);
        return mondayDate.getTime();
      })();

      const endOfWeek = (() => {
        const date = new Date(targetDate);
        const day = date.getDay();
        const diff = date.getDate() - day + (day === 0 ? 0 : 7);
        const sundayDate = new Date(date.setDate(diff));
        sundayDate.setHours(23, 59, 59, 999);
        return sundayDate.getTime();
      })();

      dateRange = {
        startOfWeek,
        endOfWeek,
        weekRange: `${new Date(startOfWeek).toLocaleDateString('vi-VN')} - ${new Date(endOfWeek).toLocaleDateString('vi-VN')}`
      };

      query.startTime = { $gte: startOfWeek };
      query.endTime = { $lte: endOfWeek };
    }
    query.status = 1;
    // Lấy danh sách ca trực (không phân trang)
    DutyShiftModel.find(query)
      .populate('officer', 'name email phone')
      .populate('unit', 'name')
      .populate('assignedBy', 'name')
      .sort({ startTime: 1 }) // Sắp xếp theo thời gian bắt đầu
      .lean()
      .exec((err, shifts) => {
        if (err) {
          return next(err);
        }

        // Nhóm ca trực theo ngày nếu có weekType
        let groupedShifts = null;
        if (weekType) {
          groupedShifts = {};
          shifts.forEach(shift => {
            const shiftDate = new Date(shift.startTime);
            const dayOfWeek = shiftDate.getDay();
            
            if (!groupedShifts[dayOfWeek]) {
              groupedShifts[dayOfWeek] = [];
            }
            
            groupedShifts[dayOfWeek].push({
              ...shift,
              dayName: getDayName(dayOfWeek),
              date: shiftDate.toISOString().split('T')[0]
            });
          });
        }

        const result = {
          shifts: weekType ? groupedShifts : shifts,
          total: shifts.length
        };

        // Thêm thông tin tuần nếu có weekType
        if (weekType) {
          result.weekInfo = {
            weekType,
            ...dateRange
          };
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: result
        });
      });
  };

  // Helper function để lấy tên ngày
  const getDayName = (dayOfWeek) => {
    const days = ['0', '1', '2', '3', '4', '5', '6'];
    return days[dayOfWeek];
  };

  async.waterfall([checkParams, getDutyShifts], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
